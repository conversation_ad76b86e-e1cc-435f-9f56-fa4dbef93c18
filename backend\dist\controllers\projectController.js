"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.syncToMingdao = exports.moveKMatrixItem = exports.moveXMatrixItem = exports.getKMatrix = exports.getXMatrix = exports.updateKMatrix = exports.updateXMatrix = exports.deleteProject = exports.updateProject = exports.getProject = exports.getProjects = exports.createProject = void 0;
const Project_1 = __importDefault(require("../models/Project"));
const fetch = require('node-fetch');
// 创建项目
const createProject = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, projectNumber, description } = req.body;
        if (!req.user || !req.user.id) {
            return res.status(401).json({ message: '用户信息无效' });
        }
        // 创建新项目
        const project = new Project_1.default({
            name,
            projectNumber,
            description,
            owner: req.user.id
        });
        // 保存项目
        yield project.save();
        res.status(201).json(project);
    }
    catch (err) {
        console.error('创建项目错误:', err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.createProject = createProject;
// 获取所有项目（仅限当前用户）
const getProjects = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const projects = yield Project_1.default.find({ owner: req.user.id });
        res.json(projects);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.getProjects = getProjects;
// 获取单个项目
const getProject = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const project = yield Project_1.default.findById(req.params.id);
        // 检查项目是否存在
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        // 检查项目所有权
        if (project.owner.toString() !== req.user.id) {
            return res.status(403).json({ message: '无权访问此项目' });
        }
        res.json(project);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.getProject = getProject;
// 更新项目
const updateProject = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { name, projectNumber, description } = req.body;
        // 查找并更新项目
        let project = yield Project_1.default.findById(req.params.id);
        // 检查项目是否存在
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        // 检查项目所有权
        if (project.owner.toString() !== req.user.id) {
            return res.status(403).json({ message: '无权修改此项目' });
        }
        // 更新项目
        project = yield Project_1.default.findByIdAndUpdate(req.params.id, { name, projectNumber, description }, { new: true });
        res.json(project);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.updateProject = updateProject;
// 删除项目
const deleteProject = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const project = yield Project_1.default.findById(req.params.id);
        // 检查项目是否存在
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        // 检查项目所有权
        if (project.owner.toString() !== req.user.id) {
            return res.status(403).json({ message: '无权删除此项目' });
        }
        // 删除项目
        yield Project_1.default.findByIdAndDelete(req.params.id);
        res.json({ message: '项目已删除' });
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.deleteProject = deleteProject;
// 更新X矩阵
const updateXMatrix = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { products, markets, goals, visions, relations, colorSettings } = req.body;
        // 查找项目
        let project = yield Project_1.default.findById(req.params.id);
        // 检查项目是否存在
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        // 检查项目所有权
        if (project.owner.toString() !== req.user.id) {
            return res.status(403).json({ message: '无权修改此项目' });
        }
        // 完全替换X矩阵数据
        project.xMatrix = {
            products: products || [],
            markets: markets || [],
            goals: goals || [],
            visions: visions || [],
            relations: relations || [],
            colorSettings: colorSettings || null
        };
        // 保存项目
        yield project.save();
        res.json(project.xMatrix);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.updateXMatrix = updateXMatrix;
// 更新K矩阵
const updateKMatrix = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { advantages, competitors, criteria, relations } = req.body;
        // 查找项目
        let project = yield Project_1.default.findById(req.params.id);
        // 检查项目是否存在
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        // 检查项目所有权
        if (project.owner.toString() !== req.user.id) {
            return res.status(403).json({ message: '无权修改此项目' });
        }
        // 完全替换K矩阵数据
        project.kMatrix = {
            advantages: advantages || [],
            competitors: competitors || [],
            criteria: criteria || [],
            relations: relations || []
        };
        // 保存项目
        yield project.save();
        res.json(project.kMatrix);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.updateKMatrix = updateKMatrix;
// 获取X矩阵
const getXMatrix = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const project = yield Project_1.default.findById(req.params.id);
        // 检查项目是否存在
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        // 检查项目所有权
        if (project.owner.toString() !== req.user.id) {
            return res.status(403).json({ message: '无权访问此项目' });
        }
        // 如果项目没有X矩阵，则创建一个空的
        if (!project.xMatrix) {
            project.xMatrix = {
                products: [],
                markets: [],
                goals: [],
                visions: [],
                relations: [],
                colorSettings: null
            };
            yield project.save();
        }
        // 按order字段排序返回数据
        // 产品/服务、目标：降序排序（order值大的在前，显示在最外侧），支持负数order值
        // 市场/客户、愿景/目的：升序排序（order值大的在后，显示在最外侧）
        const getOrderValue = (item) => item.order !== undefined ? item.order : 0;
        const sortedXMatrix = {
            products: project.xMatrix.products.sort((a, b) => getOrderValue(b) - getOrderValue(a)),
            markets: project.xMatrix.markets.sort((a, b) => getOrderValue(a) - getOrderValue(b)),
            goals: project.xMatrix.goals.sort((a, b) => getOrderValue(b) - getOrderValue(a)),
            visions: project.xMatrix.visions.sort((a, b) => getOrderValue(a) - getOrderValue(b)),
            relations: project.xMatrix.relations,
            colorSettings: project.xMatrix.colorSettings || null
        };
        res.json(sortedXMatrix);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.getXMatrix = getXMatrix;
// 获取K矩阵
const getKMatrix = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const project = yield Project_1.default.findById(req.params.id);
        // 检查项目是否存在
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        // 检查项目所有权
        if (project.owner.toString() !== req.user.id) {
            return res.status(403).json({ message: '无权访问此项目' });
        }
        // 如果项目没有K矩阵，则创建一个空的
        if (!project.kMatrix) {
            project.kMatrix = {
                advantages: [],
                competitors: [],
                criteria: [],
                relations: []
            };
            yield project.save();
        }
        // 按order字段排序返回数据
        // 竞争优势：降序排序（order值大的在前，显示在最外侧），支持负数order值
        // 竞争对手、客户选购标准：升序排序（order值大的在后，显示在最外侧）
        const getOrderValue = (item) => item.order !== undefined ? item.order : 0;
        const sortedKMatrix = {
            advantages: project.kMatrix.advantages.sort((a, b) => getOrderValue(b) - getOrderValue(a)),
            competitors: project.kMatrix.competitors.sort((a, b) => getOrderValue(a) - getOrderValue(b)),
            criteria: project.kMatrix.criteria.sort((a, b) => getOrderValue(a) - getOrderValue(b)),
            relations: project.kMatrix.relations
        };
        res.json(sortedKMatrix);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.getKMatrix = getKMatrix;
// 移动X矩阵条目
const moveXMatrixItem = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { dimension, fromIndex, toIndex } = req.body;
        // 验证参数
        if (!dimension || fromIndex === undefined || toIndex === undefined) {
            return res.status(400).json({ message: '缺少必要参数' });
        }
        // 验证维度类型
        const validDimensions = ['products', 'goals', 'markets', 'visions'];
        if (!validDimensions.includes(dimension)) {
            return res.status(400).json({ message: '无效的维度类型' });
        }
        // 查找项目
        const project = yield Project_1.default.findById(req.params.id);
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        // 检查项目所有权
        if (project.owner.toString() !== req.user.id) {
            return res.status(403).json({ message: '无权修改此项目' });
        }
        // 确保X矩阵存在
        if (!project.xMatrix) {
            return res.status(400).json({ message: 'X矩阵不存在' });
        }
        const items = project.xMatrix[dimension];
        // 验证索引范围
        if (fromIndex < 0 || fromIndex >= items.length || toIndex < 0 || toIndex >= items.length) {
            return res.status(400).json({ message: '索引超出范围' });
        }
        // 执行移动操作
        const [movedItem] = items.splice(fromIndex, 1);
        items.splice(toIndex, 0, movedItem);
        // 重新计算order值
        items.forEach((item, index) => {
            // 根据维度类型确定order值的计算方式
            if (dimension === 'products' || dimension === 'goals') {
                // 这些维度使用降序排序（order值大的在前，显示在最外侧）
                item.order = items.length - 1 - index;
            }
            else {
                // 其他维度使用升序排序（order值大的在后，显示在最外侧）
                item.order = index;
            }
        });
        // 调整关联关系的索引
        adjustXMatrixRelations(project, dimension, fromIndex, toIndex);
        // 保存项目
        yield project.save();
        // 返回更新后的X矩阵
        const getOrderValue = (item) => item.order !== undefined ? item.order : 0;
        const sortedXMatrix = {
            products: project.xMatrix.products.sort((a, b) => getOrderValue(b) - getOrderValue(a)),
            markets: project.xMatrix.markets.sort((a, b) => getOrderValue(a) - getOrderValue(b)),
            goals: project.xMatrix.goals.sort((a, b) => getOrderValue(b) - getOrderValue(a)),
            visions: project.xMatrix.visions.sort((a, b) => getOrderValue(a) - getOrderValue(b)),
            relations: project.xMatrix.relations
        };
        res.json(sortedXMatrix);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.moveXMatrixItem = moveXMatrixItem;
// 移动K矩阵条目
const moveKMatrixItem = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const { dimension, fromIndex, toIndex } = req.body;
        // 验证参数
        if (!dimension || fromIndex === undefined || toIndex === undefined) {
            return res.status(400).json({ message: '缺少必要参数' });
        }
        // 验证维度类型
        const validDimensions = ['advantages', 'competitors', 'criteria'];
        if (!validDimensions.includes(dimension)) {
            return res.status(400).json({ message: '无效的维度类型' });
        }
        // 查找项目
        const project = yield Project_1.default.findById(req.params.id);
        if (!project) {
            return res.status(404).json({ message: '项目不存在' });
        }
        // 检查项目所有权
        if (project.owner.toString() !== req.user.id) {
            return res.status(403).json({ message: '无权修改此项目' });
        }
        // 确保K矩阵存在
        if (!project.kMatrix) {
            return res.status(400).json({ message: 'K矩阵不存在' });
        }
        const items = project.kMatrix[dimension];
        // 验证索引范围
        if (fromIndex < 0 || fromIndex >= items.length || toIndex < 0 || toIndex >= items.length) {
            return res.status(400).json({ message: '索引超出范围' });
        }
        // 执行移动操作
        const [movedItem] = items.splice(fromIndex, 1);
        items.splice(toIndex, 0, movedItem);
        // 重新计算order值
        items.forEach((item, index) => {
            // 根据维度类型确定order值的计算方式
            if (dimension === 'advantages') {
                // 竞争优势使用降序排序（order值大的在前，显示在最外侧）
                item.order = items.length - 1 - index;
            }
            else {
                // 其他维度使用升序排序（order值大的在后，显示在最外侧）
                item.order = index;
            }
        });
        // 调整关联关系的索引
        adjustKMatrixRelations(project, dimension, fromIndex, toIndex);
        // 保存项目
        yield project.save();
        // 返回更新后的K矩阵
        const getOrderValue = (item) => item.order !== undefined ? item.order : 0;
        const sortedKMatrix = {
            advantages: project.kMatrix.advantages.sort((a, b) => getOrderValue(b) - getOrderValue(a)),
            competitors: project.kMatrix.competitors.sort((a, b) => getOrderValue(a) - getOrderValue(b)),
            criteria: project.kMatrix.criteria.sort((a, b) => getOrderValue(a) - getOrderValue(b)),
            relations: project.kMatrix.relations
        };
        res.json(sortedKMatrix);
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: '服务器错误' });
    }
});
exports.moveKMatrixItem = moveKMatrixItem;
// 调整X矩阵关联关系的索引
const adjustXMatrixRelations = (project, affectedDimension, fromIndex, toIndex) => {
    if (!project.xMatrix || !project.xMatrix.relations) {
        return;
    }
    // 维度名称映射
    const dimensionMap = {
        'products': 'products',
        'goals': 'goals',
        'markets': 'markets',
        'visions': 'visions'
    };
    const targetDimension = dimensionMap[affectedDimension];
    if (!targetDimension) {
        return;
    }
    project.xMatrix.relations.forEach((relation) => {
        // 检查行类型和列类型
        if (relation.rowType === targetDimension) {
            // 调整行索引
            const oldIndex = relation.rowIndex;
            if (oldIndex === fromIndex) {
                relation.rowIndex = toIndex;
            }
            else if (fromIndex < toIndex) {
                if (oldIndex > fromIndex && oldIndex <= toIndex) {
                    relation.rowIndex = oldIndex - 1;
                }
            }
            else {
                if (oldIndex >= toIndex && oldIndex < fromIndex) {
                    relation.rowIndex = oldIndex + 1;
                }
            }
        }
        if (relation.colType === targetDimension) {
            // 调整列索引
            const oldIndex = relation.colIndex;
            if (oldIndex === fromIndex) {
                relation.colIndex = toIndex;
            }
            else if (fromIndex < toIndex) {
                if (oldIndex > fromIndex && oldIndex <= toIndex) {
                    relation.colIndex = oldIndex - 1;
                }
            }
            else {
                if (oldIndex >= toIndex && oldIndex < fromIndex) {
                    relation.colIndex = oldIndex + 1;
                }
            }
        }
    });
};
// 调整K矩阵关联关系的索引
const adjustKMatrixRelations = (project, affectedDimension, fromIndex, toIndex) => {
    if (!project.kMatrix || !project.kMatrix.relations) {
        return;
    }
    project.kMatrix.relations.forEach((relation) => {
        // 检查第一个分析点类别
        if (relation.category1 === affectedDimension) {
            const oldIndex = relation.index1;
            if (oldIndex === fromIndex) {
                relation.index1 = toIndex;
            }
            else if (fromIndex < toIndex) {
                if (oldIndex > fromIndex && oldIndex <= toIndex) {
                    relation.index1 = oldIndex - 1;
                }
            }
            else {
                if (oldIndex >= toIndex && oldIndex < fromIndex) {
                    relation.index1 = oldIndex + 1;
                }
            }
        }
        // 检查第二个分析点类别
        if (relation.category2 === affectedDimension) {
            const oldIndex = relation.index2;
            if (oldIndex === fromIndex) {
                relation.index2 = toIndex;
            }
            else if (fromIndex < toIndex) {
                if (oldIndex > fromIndex && oldIndex <= toIndex) {
                    relation.index2 = oldIndex - 1;
                }
            }
            else {
                if (oldIndex >= toIndex && oldIndex < fromIndex) {
                    relation.index2 = oldIndex + 1;
                }
            }
        }
    });
};
// 同步XK矩阵数据到明道云
const syncToMingdao = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const xkMatrixData = req.body;
        // 明道云Webhook URL
        const mingdaoWebhookUrl = 'https://api.mingdao.com/workflow/hooks/Njg1NTJkZjc4M2JlYWE1MTc5MDkwYTEw';
        // 发送POST请求到明道云
        const response = yield fetch(mingdaoWebhookUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'XK-Matrix-System/1.0'
            },
            body: JSON.stringify(xkMatrixData)
        });
        const responseText = yield response.text();
        if (response.ok) {
            res.json({
                success: true,
                message: 'XK矩阵数据已成功同步到明道云',
                mingdaoResponse: responseText,
                syncTime: new Date().toISOString()
            });
        }
        else {
            res.status(500).json({
                success: false,
                message: '同步到明道云失败',
                error: responseText,
                status: response.status
            });
        }
    }
    catch (error) {
        console.error('同步到明道云时发生错误:', error);
        res.status(500).json({
            success: false,
            message: '同步到明道云时发生错误',
            error: error.message || '未知错误'
        });
    }
});
exports.syncToMingdao = syncToMingdao;
