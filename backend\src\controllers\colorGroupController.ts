import { Request, Response } from 'express';
import ColorGroup, { IColorGroup } from '../models/ColorGroup';
import User from '../models/User';

// 获取颜色组列表
export const getColorGroups = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;

    // 获取公共颜色组
    const publicColorGroups = await ColorGroup.find({ type: 'public' })
      .populate('userId', 'username')
      .sort({ createTime: -1 });

    // 获取当前用户的个人颜色组
    const privateColorGroups = await ColorGroup.find({
      type: 'private',
      userId: userId
    }).sort({ createTime: -1 });

    res.json({
      success: true,
      data: {
        public: publicColorGroups,
        private: privateColorGroups
      }
    });
  } catch (error) {
    console.error('获取颜色组失败:', error);
    res.status(500).json({ success: false, message: '获取颜色组失败' });
  }
};

// 创建颜色组
export const createColorGroup = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const { name, type, colors } = req.body;

    // 验证输入
    if (!name || !type || !colors || !Array.isArray(colors)) {
      return res.status(400).json({
        success: false,
        message: '参数不完整'
      });
    }

    // 检查用户权限
    if (type === 'public') {
      const user = await User.findById(userId);
      if (!user || user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以创建公共颜色组'
        });
      }
    }

    // 检查数量限制
    const existingCount = await ColorGroup.countDocuments({
      type,
      userId: type === 'public' ? { $exists: true } : userId
    });

    const maxCount = type === 'public' ? 7 : 8;
    if (existingCount >= maxCount) {
      return res.status(400).json({
        success: false,
        message: `${type === 'public' ? '公共' : '个人'}颜色组已达到上限(${maxCount}个)`
      });
    }

    // 检查名称是否重复
    const existingName = await ColorGroup.findOne({
      name,
      type,
      userId: type === 'public' ? { $exists: true } : userId
    });

    if (existingName) {
      return res.status(400).json({
        success: false,
        message: '颜色组名称已存在'
      });
    }

    // 创建颜色组
    const colorGroup = new ColorGroup({
      name,
      type,
      userId,
      colors
    });

    await colorGroup.save();

    res.json({
      success: true,
      data: colorGroup,
      message: '颜色组创建成功'
    });
  } catch (error) {
    console.error('创建颜色组失败:', error);
    res.status(500).json({ success: false, message: '创建颜色组失败' });
  }
};

// 更新颜色组名称
export const updateColorGroup = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '颜色组名称不能为空'
      });
    }

    // 查找颜色组
    const colorGroup = await ColorGroup.findById(id);
    if (!colorGroup) {
      return res.status(404).json({
        success: false,
        message: '颜色组不存在'
      });
    }

    // 检查权限
    const user = await User.findById(userId);
    if (colorGroup.type === 'public') {
      if (!user || user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以修改公共颜色组'
        });
      }
    } else {
      if (colorGroup.userId.toString() !== userId) {
        return res.status(403).json({
          success: false,
          message: '只能修改自己的个人颜色组'
        });
      }
    }

    // 检查名称是否重复
    const existingName = await ColorGroup.findOne({
      name,
      type: colorGroup.type,
      _id: { $ne: id },
      userId: colorGroup.type === 'public' ? { $exists: true } : colorGroup.userId
    });

    if (existingName) {
      return res.status(400).json({
        success: false,
        message: '颜色组名称已存在'
      });
    }

    // 更新颜色组
    colorGroup.name = name;
    await colorGroup.save();

    res.json({
      success: true,
      data: colorGroup,
      message: '颜色组更新成功'
    });
  } catch (error) {
    console.error('更新颜色组失败:', error);
    res.status(500).json({ success: false, message: '更新颜色组失败' });
  }
};

// 删除颜色组
export const deleteColorGroup = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user.id;
    const { id } = req.params;

    // 查找颜色组
    const colorGroup = await ColorGroup.findById(id);
    if (!colorGroup) {
      return res.status(404).json({
        success: false,
        message: '颜色组不存在'
      });
    }

    // 检查权限
    const user = await User.findById(userId);
    if (colorGroup.type === 'public') {
      if (!user || user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '只有管理员可以删除公共颜色组'
        });
      }
    } else {
      if (colorGroup.userId.toString() !== userId) {
        return res.status(403).json({
          success: false,
          message: '只能删除自己的个人颜色组'
        });
      }
    }

    // 删除颜色组
    await ColorGroup.findByIdAndDelete(id);

    res.json({
      success: true,
      message: '颜色组删除成功'
    });
  } catch (error) {
    console.error('删除颜色组失败:', error);
    res.status(500).json({ success: false, message: '删除颜色组失败' });
  }
}; 