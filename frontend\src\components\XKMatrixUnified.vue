<template>
  <div class="xk-matrix-container">
    <!-- 头部 -->
    <div class="matrix-header">
      <h1 class="matrix-title">{{ projectName || '项目' }} - XK矩阵</h1>
      <div class="header-buttons">
        <el-button type="warning" @click="addAdvantageFromUnassociatedCriteria" :disabled="getExtractableCriteriaCount() === 0">
          <i class="el-icon-plus"></i>
          抽取竞争优势
          <span v-if="getExtractableCriteriaCount() > 0" class="criteria-count">
            ({{ getExtractableCriteriaCount() }}个可抽取标准)
          </span>
        </el-button>
        <el-button type="success" @click="syncToMingdao" :loading="syncLoading">
          <i class="el-icon-upload"></i>
          更新明道云
          <span v-if="getTotalActiveRelationships() > 0" class="relationship-count">
            (含{{ getTotalActiveRelationships() }}个关联)
          </span>
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading-directive />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-alert type="error" :title="error" show-icon />
    </div>

    <!-- 统一XK矩阵表格 -->
    <div v-else class="unified-table-container">
      <!-- 颜色组管理对话框 -->
      <!-- 保存颜色组对话框 -->
      <el-dialog
        v-model="saveColorGroupDialogVisible"
        title="保存颜色组"
        width="500px"
        :close-on-click-modal="false"
      >
        <el-form :model="saveColorGroupForm" label-width="100px">
          <el-form-item label="颜色组名称" required>
            <el-input 
              v-model="saveColorGroupForm.name" 
              placeholder="请输入颜色组名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label="颜色组类型" required v-if="isAdmin">
            <el-radio-group v-model="saveColorGroupForm.type">
              <el-radio label="private">个人颜色组</el-radio>
              <el-radio label="public">公共颜色组</el-radio>
            </el-radio-group>
            <div class="form-tip">
              <small v-if="saveColorGroupForm.type === 'public'">
                公共颜色组：所有用户可见，上限7个
              </small>
              <small v-else>
                个人颜色组：仅您可见，上限8个
              </small>
            </div>
          </el-form-item>
          <el-form-item label="颜色预览">
            <div class="color-preview-list">
              <div 
                v-for="(color, index) in marketColors" 
                :key="index"
                class="color-preview-item"
                :style="{ background: color.bg }"
              >
                <span class="color-index">{{ index + 1 }}</span>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="saveColorGroupDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveColorGroup" :loading="saveColorGroupLoading">
            保存
          </el-button>
        </template>
      </el-dialog>

      <!-- 选择颜色组对话框 -->
      <el-dialog
        v-model="selectColorGroupDialogVisible"
        title="选择颜色组"
        width="600px"
        :close-on-click-modal="false"
      >
        <div class="color-group-tabs">
          <el-tabs v-model="activeColorGroupTab">
            <el-tab-pane label="公共颜色组" name="public">
              <div class="color-group-list">
                <div 
                  v-for="group in colorGroups.public" 
                  :key="group._id"
                  class="color-group-item"
                  @click="selectColorGroup(group)"
                >
                  <div class="color-group-header">
                    <span class="group-name">{{ group.name }}</span>
                    <div class="group-actions" v-if="isAdmin">
                      <el-button 
                        type="text" 
                        size="small" 
                        @click.stop="showRenameDialog(group)"
                      >
                        重命名
                      </el-button>
                      <el-button 
                        type="text" 
                        size="small" 
                        @click.stop="deleteColorGroup(group._id)"
                        style="color: #f56c6c;"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                  <div class="color-group-preview">
                    <div 
                      v-for="(color, index) in group.colors" 
                      :key="index"
                      class="color-preview-item"
                      :style="{ background: color.bg }"
                    >
                      <span class="color-index">{{ index + 1 }}</span>
                    </div>
                  </div>
                  <div class="group-info">
                    <small>创建者：{{ group.userId?.username || '未知' }}</small>
                    <small>创建时间：{{ formatDate(group.createTime) }}</small>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="个人颜色组" name="private">
              <div class="color-group-list">
                <div 
                  v-for="group in colorGroups.private" 
                  :key="group._id"
                  class="color-group-item"
                  @click="selectColorGroup(group)"
                >
                  <div class="color-group-header">
                    <span class="group-name">{{ group.name }}</span>
                    <div class="group-actions">
                      <el-button 
                        type="text" 
                        size="small" 
                        @click.stop="showRenameDialog(group)"
                      >
                        重命名
                      </el-button>
                      <el-button 
                        type="text" 
                        size="small" 
                        @click.stop="deleteColorGroup(group._id)"
                        style="color: #f56c6c;"
                      >
                        删除
                      </el-button>
                    </div>
                  </div>
                  <div class="color-group-preview">
                    <div 
                      v-for="(color, index) in group.colors" 
                      :key="index"
                      class="color-preview-item"
                      :style="{ background: color.bg }"
                    >
                      <span class="color-index">{{ index + 1 }}</span>
                    </div>
                  </div>
                  <div class="group-info">
                    <small>创建时间：{{ formatDate(group.createTime) }}</small>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        <template #footer>
          <el-button @click="selectColorGroupDialogVisible = false">取消</el-button>
        </template>
      </el-dialog>

      <!-- 重命名颜色组对话框 -->
      <el-dialog
        v-model="renameDialogVisible"
        title="重命名颜色组"
        width="400px"
        :close-on-click-modal="false"
      >
        <el-form :model="renameForm" label-width="100px">
          <el-form-item label="新名称" required>
            <el-input 
              v-model="renameForm.name" 
              placeholder="请输入新的颜色组名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="renameDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="renameColorGroup" :loading="renameLoading">
            确定
          </el-button>
        </template>
      </el-dialog>
      <!-- 颜色控制面板 -->
      <div class="color-control-panel">
        <div class="color-panel-header">
          <h3>市场/客户颜色设置</h3>
                  <div class="color-panel-actions">
          <el-button size="small" @click="resetColors">重置默认</el-button>
          <el-button size="small" @click="randomizeColors">随机颜色</el-button>
          <el-button size="small" type="primary" @click="showSaveColorGroupDialog">保存颜色组</el-button>
          <el-button size="small" type="success" @click="showSelectColorGroupDialog">选择颜色组</el-button>
        </div>
        </div>
        <div class="color-groups">
          <div
            v-for="(color, index) in marketColors"
            :key="`color-${index}`"
            class="color-group"
            :class="{ 'active': index < matrixData.markets.length }"
          >
            <div class="color-preview" :style="{ background: color.bg }">
              <span class="color-index">{{ index + 1 }}</span>
            </div>
            <div class="color-controls">
              <el-color-picker
                v-model="color.primaryColor"
                @change="updateColorGradient(index)"
                size="small"
                show-alpha
              />
              <el-color-picker
                v-model="color.secondaryColor"
                @change="updateColorGradient(index)"
                size="small"
                show-alpha
              />
            </div>
            <div class="color-label">
              <span v-if="index < matrixData.markets.length">{{ matrixData.markets[index]?.name || `市场${index + 1}` }}</span>
              <span v-else class="inactive-label">未使用</span>
            </div>
          </div>
        </div>
      </div>

      <div class="unified-matrix-wrapper">
        <table class="unified-matrix-table">
          <tbody>
            <!-- 上方行：从内向外渲染，确保产品和竞争优势都紧贴矩阵中心 -->
            <tr v-for="(rowIndex) in Math.max(matrixData.products.length, matrixData.advantages.length)" :key="`top-row-${rowIndex}`" class="top-combined-row">
              <!-- 目标列的交汇单元格 - 数量少时从下方开始渲染的产品交汇点 -->
              <td v-for="(goal, gIndex) in matrixData.goals" :key="`intersection-top${rowIndex}-g${gIndex}`"
                  class="intersection-cell"
                  v-if="getProductIndex(rowIndex) !== null"
                  :style="getPathCellStyle(rowIndex, 'goals', gIndex)"
                  @click="toggleIntersection('product-goal', getProductIndex(rowIndex), gIndex)">
                <span v-if="getIntersectionState('product-goal', getProductIndex(rowIndex), gIndex)" class="check-mark" style="color: #000000 !important; text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;">✓</span>
              </td>
              <td v-else v-for="(goal, gIndex) in matrixData.goals" :key="`empty-top${rowIndex}-g${gIndex}`"
                  class="intersection-cell">
              </td>

              <!-- 产品/服务单元格 - 数量少时从下方开始渲染，保持紧贴X矩阵 -->
              <td v-if="getProductIndex(rowIndex) !== null" class="item-cell-container x-matrix-width">
                <div
                  class="item-cell product-cell"
                  :class="{ 'dragging': dragState.dragging && dragState.dragType === 'products' && dragState.dragIndex === getProductIndex(rowIndex) }"
                  :style="{ background: getProductColor(getProductIndex(rowIndex)).bg }"
                  @dblclick="startEdit('products', getProductIndex(rowIndex))"

                  draggable="true"
                  @dragstart="startDrag($event, 'products', getProductIndex(rowIndex))"
                  @dragover="handleDragOver($event, 'products', getProductIndex(rowIndex))"
                  @drop="handleDrop($event, 'products', getProductIndex(rowIndex))"
                  @dragend="endDrag"
                >
                  <!-- 拖拽指示器 -->
                  <div v-if="dragState.showDropIndicator && dragState.dropType === 'products' && dragState.dropIndex === getProductIndex(rowIndex)"
                       class="drop-indicator"
                       :class="dragState.dropPosition">
                  </div>
                  <div class="cell-content">
                    <input
                      v-if="editingItem.type === 'products' && editingItem.index === getProductIndex(rowIndex)"
                      v-model="editingItem.name"
                      class="item-input"
                      @blur="saveEdit"
                      @keyup.enter="saveEdit"
                      @keyup.escape="cancelEdit"
                      ref="editInput"
                    />
                    <span
                      v-else
                      class="item-text"
                    >{{ matrixData.products[getProductIndex(rowIndex)].name }}
                      <!-- 拖拽手柄 -->
                      <span class="drag-handle inline" title="拖拽移动">
                        <i class="el-icon-rank"></i>
                      </span>
                    </span>
                    <div class="item-actions">
                      <el-button type="text" size="mini" @click.stop="startEdit('products', getProductIndex(rowIndex))" title="编辑">
                        <i class="el-icon-edit"></i>
                      </el-button>
                    </div>
                    <div class="delete-icon" @click.stop="deleteItem('products', getProductIndex(rowIndex))" title="删除">
                      <i class="el-icon-delete"></i>
                    </div>
                    <!-- 产品维度的添加按钮 - 显示在最外侧的产品条目角上 -->
                    <div v-if="getProductIndex(rowIndex) === 0" class="corner-add-btn top-left" @click.stop="addItemDirect('products')" title="添加产品/服务">
                      <i class="el-icon-plus"></i>
                    </div>
                    <!-- 产品维度的位置添加按钮 - 在当前条目后添加 -->
                    <div class="position-add-btn right" @click.stop="addItemAtPosition('products', getProductIndex(rowIndex))" title="在此位置后添加产品/服务">
                      <i class="el-icon-plus"></i>
                    </div>

                  </div>
                </div>
              </td>
              <td v-else class="matrix-separator x-matrix-width"></td>

              <!-- 市场列的交汇单元格 - 数量少时从下方开始渲染的产品交汇点 -->
              <td v-for="(market, mIndex) in matrixData.markets" :key="`intersection-top${rowIndex}-m${mIndex}`"
                  class="intersection-cell"
                  :class="{
                    'associated': getIntersectionState('product-market', getProductIndex(rowIndex), mIndex)
                  }"
                  :style="getIntersectionState('product-market', getProductIndex(rowIndex), mIndex) ?
                    `--market-color: ${marketColors[mIndex % marketColors.length].bg}; --market-border-color: ${marketColors[mIndex % marketColors.length].primaryColor}; background: ${marketColors[mIndex % marketColors.length].bg} !important; background-color: ${marketColors[mIndex % marketColors.length].bg} !important; color: white !important; border-color: #ddd !important;` :
                    getPathCellStyle(rowIndex, 'markets', mIndex)"
                  v-if="getProductIndex(rowIndex) !== null"
                  @click="toggleIntersection('product-market', getProductIndex(rowIndex), mIndex)"
                  :title="`产品 ${matrixData.products[getProductIndex(rowIndex)]?.name} - 市场 ${matrixData.markets[mIndex]?.name}`"
                  :data-debug="`row${rowIndex}-market${mIndex}`">
                <span v-if="getIntersectionState('product-market', getProductIndex(rowIndex), mIndex)" class="check-mark">✓</span>
              </td>
              <td v-else v-for="(market, mIndex) in matrixData.markets" :key="`empty-top${rowIndex}-m${mIndex}`"
                  class="intersection-cell"
                  :style="getPathCellStyle(rowIndex, 'markets', mIndex)">
              </td>

              <!-- X矩阵和K矩阵之间的分隔列 - 根据市场与客户条目数量动态调整 -->
              <!-- 当没有市场条目时，隔一列；当有市场条目时，不需要分隔列 -->
              <td v-if="matrixData.markets.length === 0" class="matrix-separator-column"></td>

              <!-- 竞争优势单元格 - 数量少时从下方开始渲染，保持紧贴K矩阵 -->
              <td v-if="getAdvantageIndex(rowIndex) !== null" class="item-cell-container k-matrix-width">
                <div
                  class="item-cell advantage-cell"
                  :class="{ 'dragging': dragState.dragging && dragState.dragType === 'advantages' && dragState.dragIndex === getAdvantageIndex(rowIndex) }"
                  @dblclick="startEdit('advantages', getAdvantageIndex(rowIndex))"
                  draggable="true"
                  @dragstart="startDrag($event, 'advantages', getAdvantageIndex(rowIndex))"
                  @dragover="handleDragOver($event, 'advantages', getAdvantageIndex(rowIndex))"
                  @drop="handleDrop($event, 'advantages', getAdvantageIndex(rowIndex))"
                  @dragend="endDrag"
                >
                  <!-- 拖拽指示器 -->
                  <div v-if="dragState.showDropIndicator && dragState.dropType === 'advantages' && dragState.dropIndex === getAdvantageIndex(rowIndex)"
                       class="drop-indicator"
                       :class="dragState.dropPosition">
                  </div>
                  <div class="cell-content">
                    <input
                      v-if="editingItem.type === 'advantages' && editingItem.index === getAdvantageIndex(rowIndex)"
                      v-model="editingItem.name"
                      class="item-input"
                      @blur="saveEdit"
                      @keyup.enter="saveEdit"
                      @keyup.escape="cancelEdit"
                      ref="editInput"
                    />
                    <span
                      v-else
                      class="item-text"
                    >{{ matrixData.advantages[getAdvantageIndex(rowIndex)].name }}
                      <!-- 拖拽手柄 -->
                      <span class="drag-handle inline" title="拖拽移动">
                        <i class="el-icon-rank"></i>
                      </span>
                    </span>
                    <div class="item-actions">
                      <el-button type="text" size="mini" @click.stop="startEdit('advantages', getAdvantageIndex(rowIndex))" title="编辑">
                        <i class="el-icon-edit"></i>
                      </el-button>
                    </div>
                    <div class="delete-icon" @click.stop="deleteItem('advantages', getAdvantageIndex(rowIndex))" title="删除">
                      <i class="el-icon-delete"></i>
                    </div>
                    <!-- 竞争优势维度的添加按钮 - 显示在最外侧的竞争优势条目角上 -->
                    <div v-if="getAdvantageIndex(rowIndex) === 0" class="corner-add-btn top-left" @click.stop="addItemDirect('advantages')" title="添加竞争优势">
                      <i class="el-icon-plus"></i>
                    </div>
                    <!-- 竞争优势维度的位置添加按钮 - 在当前条目后添加 -->
                    <div class="position-add-btn right" @click.stop="addItemAtPosition('advantages', getAdvantageIndex(rowIndex))" title="在此位置后添加竞争优势">
                      <i class="el-icon-plus"></i>
                    </div>

                  </div>
                </div>
              </td>
              <td v-else class="matrix-separator k-matrix-width"></td>

              <!-- 竞争对手列的交汇单元格 - 只有对应的竞争优势存在时才可点击 -->
              <td v-for="(competitor, cIndex) in matrixData.competitors" :key="`intersection-top${rowIndex}-c${cIndex}`"
                  class="intersection-cell"
                  v-if="getAdvantageIndex(rowIndex) !== null"
                  @click="toggleIntersection('advantage-competitor', getAdvantageIndex(rowIndex), cIndex)">
                <span v-if="getIntersectionState('advantage-competitor', getAdvantageIndex(rowIndex), cIndex)" class="check-mark" style="color: #000000 !important; text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;">✓</span>
              </td>
              <td v-else v-for="(competitor, cIndex) in matrixData.competitors" :key="`empty-top${rowIndex}-c${cIndex}`"
                  class="intersection-cell">
              </td>
            </tr>

            <!-- 中间行：目标列 + X矩阵中心 + 市场列 + K矩阵中心 + 竞争对手列 -->
            <tr class="main-center-row">
              <!-- 目标列 - 每个目标占一列 -->
              <td v-for="(goal, gIndex) in matrixData.goals" :key="`goal-col-${gIndex}`"
                  class="item-cell goal-cell"
                  :class="{ 'dragging': dragState.dragging && dragState.dragType === 'goals' && dragState.dragIndex === gIndex }"
                  @dblclick="startEdit('goals', gIndex)"
                  draggable="true"
                  @dragstart="startDrag($event, 'goals', gIndex)"
                  @dragover="handleDragOver($event, 'goals', gIndex)"
                  @drop="handleDrop($event, 'goals', gIndex)"
                  @dragend="endDrag"
              >
                <!-- 拖拽指示器 -->
                <div v-if="dragState.showDropIndicator && dragState.dropType === 'goals' && dragState.dropIndex === gIndex"
                     class="drop-indicator"
                     :class="dragState.dropPosition">
                </div>
                <div class="cell-content vertical">
                  <input
                    v-if="editingItem.type === 'goals' && editingItem.index === gIndex"
                    v-model="editingItem.name"
                    class="item-input vertical"
                    @blur="saveEdit"
                    @keyup.enter="saveEdit"
                    @keyup.escape="cancelEdit"
                    ref="editInput"
                  />
                  <span
                    v-else
                    class="item-text"
                  >{{ goal.name }}
                    <!-- 拖拽手柄 -->
                    <span class="drag-handle inline vertical" title="拖拽移动">
                      <i class="el-icon-rank"></i>
                    </span>
                  </span>
                  <div class="item-actions">
                    <el-button type="text" size="mini" @click.stop="startEdit('goals', gIndex)" title="编辑">
                      <i class="el-icon-edit"></i>
                    </el-button>
                  </div>
                  <div class="delete-icon vertical" @click.stop="deleteItem('goals', gIndex)" title="删除">
                    <i class="el-icon-delete"></i>
                  </div>
                  <!-- 目标维度的添加按钮 - 显示在最外侧的目标条目角上 -->
                  <div v-if="gIndex === 0" class="corner-add-btn bottom-left" @click.stop="addItemDirect('goals')" title="添加目标">
                    <i class="el-icon-plus"></i>
                  </div>
                  <!-- 目标维度的位置添加按钮 - 在当前条目后添加 -->
                  <div class="position-add-btn bottom" @click.stop="addItemAtPosition('goals', gIndex)" title="在此位置后添加目标">
                    <i class="el-icon-plus"></i>
                  </div>

                </div>
              </td>

              <!-- X矩阵中心 -->
              <td class="matrix-center-cell x-matrix-center">
                <div class="matrix-center">
                  <svg viewBox="0 0 320 240" class="matrix-svg">
                    <rect x="0" y="0" width="320" height="240" fill="#e8f4fd" stroke="none"/>
                    <line x1="0" y1="0" x2="320" y2="240" stroke="#333" stroke-width="3"/>
                    <line x1="320" y1="0" x2="0" y2="240" stroke="#333" stroke-width="3"/>
                    <text x="160" y="40" text-anchor="middle" class="area-label">Products/Services</text>
                    <text x="160" y="65" text-anchor="middle" class="area-label-chinese">产品与服务</text>
                    <text x="50" y="125" text-anchor="middle" class="area-label">Goals</text>
                    <text x="50" y="150" text-anchor="middle" class="area-label-chinese">目标(定量)</text>
                    <text x="270" y="125" text-anchor="middle" class="area-label">Markets</text>
                    <text x="270" y="150" text-anchor="middle" class="area-label-chinese">市场与客户</text>
                    <text x="160" y="205" text-anchor="middle" class="area-label">Objective</text>
                    <text x="160" y="230" text-anchor="middle" class="area-label-chinese">愿景/目的(定性)</text>
                  </svg>
                  <!-- 空矩阵时的初始添加按钮 - 显示在矩阵顶点 -->
                  <div v-if="matrixData.products.length === 0" class="corner-add-btn matrix-corner-btn products-corner" @click.stop="addItemDirect('products')" title="添加产品/服务">
                    <span class="add-icon">+</span>
                  </div>
                  <div v-if="matrixData.goals.length === 0" class="corner-add-btn matrix-corner-btn goals-corner" @click.stop="addItemDirect('goals')" title="添加目标">
                    <span class="add-icon">+</span>
                  </div>
                  <div v-if="matrixData.markets.length === 0" class="corner-add-btn matrix-corner-btn markets-corner" @click.stop="addItemDirect('markets')" title="添加市场/客户">
                    <span class="add-icon">+</span>
                  </div>
                  <div v-if="matrixData.visions.length === 0" class="corner-add-btn matrix-corner-btn visions-corner" @click.stop="addItemDirect('visions')" title="添加愿景/目的">
                    <span class="add-icon">+</span>
                  </div>
                </div>
              </td>

              <!-- 市场/客户列 - 每个市场占一列 -->
              <td v-for="(market, mIndex) in matrixData.markets" :key="`market-col-${mIndex}`"
                  class="item-cell market-cell"
                  :class="{ 'dragging': dragState.dragging && dragState.dragType === 'markets' && dragState.dragIndex === mIndex }"
                  :style="{ background: getMarketColor(mIndex).bg }"
                  @dblclick="startEdit('markets', mIndex)"

                  draggable="true"
                  @dragstart="startDrag($event, 'markets', mIndex)"
                  @dragover="handleDragOver($event, 'markets', mIndex)"
                  @drop="handleDrop($event, 'markets', mIndex)"
                  @dragend="endDrag"
              >
                <!-- 拖拽指示器 -->
                <div v-if="dragState.showDropIndicator && dragState.dropType === 'markets' && dragState.dropIndex === mIndex"
                     class="drop-indicator"
                     :class="dragState.dropPosition">
                </div>
                <div class="cell-content vertical">
                  <input
                    v-if="editingItem.type === 'markets' && editingItem.index === mIndex"
                    v-model="editingItem.name"
                    class="item-input vertical"
                    @blur="saveEdit"
                    @keyup.enter="saveEdit"
                    @keyup.escape="cancelEdit"
                    ref="editInput"
                  />
                  <span
                    v-else
                    class="item-text"
                  >{{ market.name }}
                    <!-- 拖拽手柄 -->
                    <span class="drag-handle inline vertical" title="拖拽移动">
                      <i class="el-icon-rank"></i>
                    </span>
                  </span>
                  <div class="item-actions">
                    <el-button type="text" size="mini" @click.stop="startEdit('markets', mIndex)" title="编辑">
                      <i class="el-icon-edit"></i>
                    </el-button>
                  </div>
                  <div class="delete-icon vertical" @click.stop="deleteItem('markets', mIndex)" title="删除">
                    <i class="el-icon-delete"></i>
                  </div>
                  <!-- 市场维度的添加按钮 - 显示在最外侧的市场条目角上 -->
                  <div v-if="mIndex === matrixData.markets.length - 1" class="corner-add-btn bottom-right" @click.stop="addItemDirect('markets')" title="添加市场/客户">
                    <i class="el-icon-plus"></i>
                  </div>
                  <!-- 市场维度的位置添加按钮 - 在当前条目后添加 -->
                  <div class="position-add-btn bottom" @click.stop="addItemAtPosition('markets', mIndex)" title="在此位置后添加市场/客户">
                    <i class="el-icon-plus"></i>
                  </div>

                </div>
              </td>

              <!-- X矩阵和K矩阵之间的分隔列 - 根据市场与客户条目数量动态调整 -->
              <!-- 当没有市场条目时，隔一列；当有市场条目时，不需要分隔列 -->
              <td v-if="matrixData.markets.length === 0" class="matrix-separator-column"></td>

              <!-- K矩阵中心 -->
              <td class="matrix-center-cell k-matrix-center">
                <div class="matrix-center">
                  <svg viewBox="0 0 320 240" class="matrix-svg">
                    <rect x="0" y="0" width="320" height="240" fill="#f0e6f7" stroke="none"/>
                    <line x1="0" y1="120" x2="320" y2="0" stroke="#333" stroke-width="3"/>
                    <line x1="0" y1="120" x2="320" y2="240" stroke="#333" stroke-width="3"/>
                    <text x="100" y="35" text-anchor="middle" class="area-label">Competitive Advantages</text>
                    <text x="100" y="55" text-anchor="middle" class="area-label-chinese">(或者打造凸显) 竞争优势</text>
                    <text x="270" y="125" text-anchor="middle" class="area-label">Competitors</text>
                    <text x="270" y="150" text-anchor="middle" class="area-label-chinese">竞争对手</text>
                    <text x="100" y="205" text-anchor="middle" class="area-label">Buying Criterias</text>
                    <text x="100" y="230" text-anchor="middle" class="area-label-chinese">客户选购标准</text>
                  </svg>
                  <!-- 空K矩阵时的初始添加按钮 - 显示在矩阵顶点 -->
                  <div v-if="matrixData.advantages.length === 0" class="corner-add-btn matrix-corner-btn advantages-corner" @click.stop="addItemDirect('advantages')" title="添加竞争优势">
                    <span class="add-icon">+</span>
                  </div>
                  <div v-if="matrixData.competitors.length === 0" class="corner-add-btn matrix-corner-btn competitors-corner" @click.stop="addItemDirect('competitors')" title="添加竞争对手">
                    <span class="add-icon">+</span>
                  </div>
                  <div v-if="matrixData.criteria.length === 0" class="corner-add-btn matrix-corner-btn criteria-corner" @click.stop="addItemDirect('criteria')" title="添加选购标准">
                    <span class="add-icon">+</span>
                  </div>
                </div>
              </td>

              <!-- 竞争对手列 - 每个竞争对手占一列 -->
              <td v-for="(competitor, cIndex) in matrixData.competitors" :key="`competitor-col-${cIndex}`"
                  class="item-cell competitor-cell"
                  :class="{ 'dragging': dragState.dragging && dragState.dragType === 'competitors' && dragState.dragIndex === cIndex }"
                  @dblclick="startEdit('competitors', cIndex)"
                  draggable="true"
                  @dragstart="startDrag($event, 'competitors', cIndex)"
                  @dragover="handleDragOver($event, 'competitors', cIndex)"
                  @drop="handleDrop($event, 'competitors', cIndex)"
                  @dragend="endDrag"
              >
                <!-- 拖拽指示器 -->
                <div v-if="dragState.showDropIndicator && dragState.dropType === 'competitors' && dragState.dropIndex === cIndex"
                     class="drop-indicator"
                     :class="dragState.dropPosition">
                </div>
                <div class="cell-content vertical">
                  <input
                    v-if="editingItem.type === 'competitors' && editingItem.index === cIndex"
                    v-model="editingItem.name"
                    class="item-input vertical"
                    @blur="saveEdit"
                    @keyup.enter="saveEdit"
                    @keyup.escape="cancelEdit"
                    ref="editInput"
                  />
                  <span
                    v-else
                    class="item-text"
                  >{{ competitor.name }}
                    <!-- 拖拽手柄 -->
                    <span class="drag-handle inline vertical" title="拖拽移动">
                      <i class="el-icon-rank"></i>
                    </span>
                  </span>
                  <div class="item-actions">
                    <el-button type="text" size="mini" @click.stop="startEdit('competitors', cIndex)" title="编辑">
                      <i class="el-icon-edit"></i>
                    </el-button>
                  </div>
                  <div class="delete-icon vertical" @click.stop="deleteItem('competitors', cIndex)" title="删除">
                    <i class="el-icon-delete"></i>
                  </div>
                  <!-- 竞争对手维度的添加按钮 - 显示在最外侧的竞争对手条目角上 -->
                  <div v-if="cIndex === matrixData.competitors.length - 1" class="corner-add-btn bottom-right" @click.stop="addItemDirect('competitors')" title="添加竞争对手">
                    <i class="el-icon-plus"></i>
                  </div>
                  <!-- 竞争对手维度的位置添加按钮 - 在当前条目后添加 -->
                  <div class="position-add-btn bottom" @click.stop="addItemAtPosition('competitors', cIndex)" title="在此位置后添加竞争对手">
                    <i class="el-icon-plus"></i>
                  </div>

                </div>
              </td>
            </tr>

            <!-- 下方行：只根据客户选购标准的数量来渲染 -->
            <tr v-for="(rowIndex) in matrixData.criteria.length" :key="`k-bottom-row-${rowIndex}`" class="bottom-combined-row">
              <!-- 目标列的交汇单元格 - 只有对应的愿景存在时才可点击 -->
              <td v-for="(goal, gIndex) in matrixData.goals" :key="`intersection-bottom${rowIndex}-g${gIndex}`"
                  class="intersection-cell"
                  v-if="matrixData.visions[rowIndex - 1]"
                  @click="toggleIntersection('vision-goal', rowIndex - 1, gIndex)">
                <span v-if="getIntersectionState('vision-goal', rowIndex - 1, gIndex)" class="check-mark" style="color: #000000 !important; text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;">✓</span>
              </td>
              <td v-else v-for="(goal, gIndex) in matrixData.goals" :key="`empty-bottom${rowIndex}-g${gIndex}`"
                  class="intersection-cell">
              </td>

              <!-- 愿景/目的单元格 - 只有对应的愿景存在时才显示 -->
              <td v-if="matrixData.visions[rowIndex - 1]" class="item-cell-container x-matrix-width">
                <div
                  class="item-cell vision-cell"
                  :class="{ 'dragging': dragState.dragging && dragState.dragType === 'visions' && dragState.dragIndex === rowIndex - 1 }"
                  @dblclick="startEdit('visions', rowIndex - 1)"
                  draggable="true"
                  @dragstart="startDrag($event, 'visions', rowIndex - 1)"
                  @dragover="handleDragOver($event, 'visions', rowIndex - 1)"
                  @drop="handleDrop($event, 'visions', rowIndex - 1)"
                  @dragend="endDrag"
                >
                  <!-- 拖拽指示器 -->
                  <div v-if="dragState.showDropIndicator && dragState.dropType === 'visions' && dragState.dropIndex === rowIndex - 1"
                       class="drop-indicator"
                       :class="dragState.dropPosition">
                  </div>
                  <div class="cell-content">
                    <input
                      v-if="editingItem.type === 'visions' && editingItem.index === rowIndex - 1"
                      v-model="editingItem.name"
                      class="item-input"
                      @blur="saveEdit"
                      @keyup.enter="saveEdit"
                      @keyup.escape="cancelEdit"
                      ref="editInput"
                    />
                    <span
                      v-else
                      class="item-text"
                    >{{ matrixData.visions[rowIndex - 1].name }}
                      <!-- 拖拽手柄 -->
                      <span class="drag-handle inline" title="拖拽移动">
                        <i class="el-icon-rank"></i>
                      </span>
                    </span>
                    <div class="item-actions">
                      <el-button type="text" size="mini" @click.stop="startEdit('visions', rowIndex - 1)" title="编辑">
                        <i class="el-icon-edit"></i>
                      </el-button>
                    </div>
                    <div class="delete-icon" @click.stop="deleteItem('visions', rowIndex - 1)" title="删除">
                      <i class="el-icon-delete"></i>
                    </div>
                    <!-- 愿景/目的维度的添加按钮 - 显示在最外侧的愿景条目角上 -->
                    <div v-if="rowIndex - 1 === matrixData.visions.length - 1" class="corner-add-btn bottom-left" @click.stop="addItemDirect('visions')" title="添加愿景/目的">
                      <i class="el-icon-plus"></i>
                    </div>
                    <!-- 愿景/目的维度的位置添加按钮 - 在当前条目后添加 -->
                    <div class="position-add-btn right" @click.stop="addItemAtPosition('visions', rowIndex - 1)" title="在此位置后添加愿景/目的">
                      <i class="el-icon-plus"></i>
                    </div>

                  </div>
                </div>
              </td>
              <td v-else class="matrix-separator x-matrix-width"></td>

              <!-- 市场列的交汇单元格 - 客户选购标准与市场关联（每个选购标准只能关联一个市场） -->
              <td v-for="(market, mIndex) in matrixData.markets" :key="`intersection-bottom${rowIndex}-m${mIndex}`"
                  class="intersection-cell"
                  :class="{
                    'associated': getIntersectionState('criteria-market', rowIndex - 1, mIndex),
                    'disabled': isCriteriaAssociatedWithOtherMarket(rowIndex - 1, mIndex)
                  }"
                  :style="getIntersectionState('criteria-market', rowIndex - 1, mIndex) ?
                    `--market-color: ${getMarketColor(mIndex).bg}; --market-border-color: ${getMarketColor(mIndex).primaryColor}; background: ${getMarketColor(mIndex).bg} !important; background-color: ${getMarketColor(mIndex).bg} !important; color: white !important; border-color: #ddd !important;` :
                    getCriteriaPathCellStyle(rowIndex, 'markets', mIndex)"
                  v-if="matrixData.criteria[rowIndex - 1]"
                  @click="toggleIntersection('criteria-market', rowIndex - 1, mIndex)"
                  :title="getCriteriaMarketTooltip(rowIndex - 1, mIndex)">
                <span v-if="getIntersectionState('criteria-market', rowIndex - 1, mIndex)" class="check-mark">✓</span>
              </td>
              <td v-else v-for="(market, mIndex) in matrixData.markets" :key="`empty-bottom${rowIndex}-m${mIndex}`"
                  class="intersection-cell criteria-row-cell"
                  :style="getCriteriaPathCellStyle(rowIndex, 'markets', mIndex)"
                  :title="`调试: 客户选购标准行 rowIndex=${rowIndex}, mIndex=${mIndex}`">
              </td>

              <!-- X矩阵和K矩阵之间的分隔列 - 根据市场与客户条目数量动态调整 -->
              <!-- 当没有市场条目时，隔一列；当有市场条目时，不需要分隔列 -->
              <td v-if="matrixData.markets.length === 0" class="matrix-separator-column"></td>

              <!-- 客户选购标准单元格 - 始终显示 -->
              <td class="item-cell-container k-matrix-width">
                <div
                  class="item-cell criteria-cell"
                  :class="{ 'dragging': dragState.dragging && dragState.dragType === 'criteria' && dragState.dragIndex === rowIndex - 1 }"
                  :style="{
                    background: getCriteriaColor(rowIndex - 1).bg,
                    boxShadow: `0 2px 8px ${getCriteriaColor(rowIndex - 1).shadow}`
                  }"
                  @dblclick="startEdit('criteria', rowIndex - 1)"
                  draggable="true"
                  @dragstart="startDrag($event, 'criteria', rowIndex - 1)"
                  @dragover="handleDragOver($event, 'criteria', rowIndex - 1)"
                  @drop="handleDrop($event, 'criteria', rowIndex - 1)"
                  @dragend="endDrag"
                >
                  <!-- 拖拽指示器 -->
                  <div v-if="dragState.showDropIndicator && dragState.dropType === 'criteria' && dragState.dropIndex === rowIndex - 1"
                       class="drop-indicator"
                       :class="dragState.dropPosition">
                  </div>
                  <div class="cell-content">
                    <input
                      v-if="editingItem.type === 'criteria' && editingItem.index === rowIndex - 1"
                      v-model="editingItem.name"
                      class="item-input"
                      @blur="saveEdit"
                      @keyup.enter="saveEdit"
                      @keyup.escape="cancelEdit"
                      ref="editInput"
                    />
                    <span
                      v-else
                      class="item-text"
                    >{{ matrixData.criteria[rowIndex - 1].name }}
                      <!-- 拖拽手柄 -->
                      <span class="drag-handle inline" title="拖拽移动">
                        <i class="el-icon-rank"></i>
                      </span>
                    </span>
                    <div class="item-actions">
                      <el-button type="text" size="mini" @click.stop="startEdit('criteria', rowIndex - 1)" title="编辑">
                        <i class="el-icon-edit"></i>
                      </el-button>
                    </div>
                    <div class="delete-icon" @click.stop="deleteItem('criteria', rowIndex - 1)" title="删除">
                      <i class="el-icon-delete"></i>
                    </div>
                    <!-- 客户选购标准维度的添加按钮 - 显示在最外侧的客户选购标准条目角上 -->
                    <div v-if="rowIndex - 1 === matrixData.criteria.length - 1" class="corner-add-btn bottom-left" @click.stop="addItemDirect('criteria')" title="添加选购标准">
                      <i class="el-icon-plus"></i>
                    </div>
                    <!-- 客户选购标准维度的位置添加按钮 - 在当前条目后添加 -->
                    <div class="position-add-btn right" @click.stop="addItemAtPosition('criteria', rowIndex - 1)" title="在此位置后添加选购标准">
                      <i class="el-icon-plus"></i>
                    </div>

                  </div>
                </div>
              </td>

              <!-- 竞争对手列的交汇单元格 -->
              <td v-for="(competitor, cIndex) in matrixData.competitors" :key="`intersection-bottom${rowIndex}-c${cIndex}`"
                  class="intersection-cell"
                  @click="toggleIntersection('criteria-competitor', rowIndex - 1, cIndex)">
                <span v-if="getIntersectionState('criteria-competitor', rowIndex - 1, cIndex)" class="check-mark" style="color: #000000 !important; text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;">✓</span>
              </td>
            </tr>

            <!-- X矩阵独有的愿景/目的行（当愿景比客户选购标准多时） -->
            <tr v-for="(rowIndex) in Math.max(0, matrixData.visions.length - matrixData.criteria.length)" :key="`x-only-bottom-row-${rowIndex}`" class="bottom-combined-row">
              <!-- 目标列的交汇单元格 -->
              <td v-for="(goal, gIndex) in matrixData.goals" :key="`intersection-x-only-bottom${rowIndex}-g${gIndex}`"
                  class="intersection-cell"
                  @click="toggleIntersection('vision-goal', matrixData.criteria.length + rowIndex - 1, gIndex)">
                <span v-if="getIntersectionState('vision-goal', matrixData.criteria.length + rowIndex - 1, gIndex)" class="check-mark" style="color: #000000 !important; text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8) !important;">✓</span>
              </td>

              <!-- 愿景/目的单元格 -->
              <td class="item-cell-container x-matrix-width">
                <div
                  class="item-cell vision-cell"
                  :class="{ 'dragging': dragState.dragging && dragState.dragType === 'visions' && dragState.dragIndex === matrixData.criteria.length + rowIndex - 1 }"
                  @dblclick="startEdit('visions', matrixData.criteria.length + rowIndex - 1)"
                  draggable="true"
                  @dragstart="startDrag($event, 'visions', matrixData.criteria.length + rowIndex - 1)"
                  @dragover="handleDragOver($event, 'visions', matrixData.criteria.length + rowIndex - 1)"
                  @drop="handleDrop($event, 'visions', matrixData.criteria.length + rowIndex - 1)"
                  @dragend="endDrag"
                >
                  <!-- 拖拽指示器 -->
                  <div v-if="dragState.showDropIndicator && dragState.dropType === 'visions' && dragState.dropIndex === matrixData.criteria.length + rowIndex - 1"
                       class="drop-indicator"
                       :class="dragState.dropPosition">
                  </div>
                  <div class="cell-content">
                    <input
                      v-if="editingItem.type === 'visions' && editingItem.index === matrixData.criteria.length + rowIndex - 1"
                      v-model="editingItem.name"
                      class="item-input"
                      @blur="saveEdit"
                      @keyup.enter="saveEdit"
                      @keyup.escape="cancelEdit"
                      ref="editInput"
                    />
                    <span
                      v-else
                      class="item-text"
                    >{{ matrixData.visions[matrixData.criteria.length + rowIndex - 1].name }}
                      <!-- 拖拽手柄 -->
                      <span class="drag-handle inline" title="拖拽移动">
                        <i class="el-icon-rank"></i>
                      </span>
                    </span>
                    <div class="item-actions">
                      <el-button type="text" size="mini" @click.stop="startEdit('visions', matrixData.criteria.length + rowIndex - 1)" title="编辑">
                        <i class="el-icon-edit"></i>
                      </el-button>
                    </div>
                    <div class="delete-icon" @click.stop="deleteItem('visions', matrixData.criteria.length + rowIndex - 1)" title="删除">
                      <i class="el-icon-delete"></i>
                    </div>
                    <!-- 愿景/目的维度的添加按钮 - 显示在最外侧的愿景条目角上 -->
                    <div v-if="matrixData.criteria.length + rowIndex - 1 === matrixData.visions.length - 1" class="corner-add-btn bottom-left" @click.stop="addItemDirect('visions')" title="添加愿景/目的">
                      <i class="el-icon-plus"></i>
                    </div>
                    <!-- 愿景/目的维度的位置添加按钮 - 在当前条目后添加 -->
                    <div class="position-add-btn right" @click.stop="addItemAtPosition('visions', matrixData.criteria.length + rowIndex - 1)" title="在此位置后添加愿景/目的">
                      <i class="el-icon-plus"></i>
                    </div>

                  </div>
                </div>
              </td>

              <!-- 市场列的交汇单元格 - 愿景与市场不能关联，显示为禁用状态 -->
              <td v-for="(market, mIndex) in matrixData.markets" :key="`intersection-x-only-bottom${rowIndex}-m${mIndex}`"
                  class="intersection-cell disabled-cell"
                  title="愿景/目的与市场不能关联">
                <span class="disabled-mark">×</span>
              </td>

              <!-- X矩阵和K矩阵之间的分隔列 - 根据市场与客户条目数量动态调整 -->
              <!-- 当没有市场条目时，隔一列；当有市场条目时，不需要分隔列 -->
              <td v-if="matrixData.markets.length === 0" class="matrix-separator-column"></td>

              <!-- K矩阵区域的空白单元格 -->
              <td class="matrix-separator k-matrix-width"></td>
              <td v-for="(competitor, cIndex) in matrixData.competitors" :key="`empty-k-bottom${rowIndex}-c${cIndex}`"
                  class="intersection-cell">
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>




  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { projectApi, colorGroupApi } from '../api';

export default defineComponent({
  name: 'XKMatrixUnified',
  props: {
    projectId: {
      type: String,
      required: true
    },
    projectName: {
      type: String,
      default: ''
    },
    projectNumber: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const loading = ref(false);
    const error = ref('');
    const syncLoading = ref(false);
    
    // 编辑状态管理
    const editingItem = reactive({
      type: '',
      index: -1,
      name: '',
      originalName: ''
    });

    // 拖拽状态管理
    const dragState = reactive({
      dragging: false,
      dragType: '',
      dragIndex: -1,
      showDropIndicator: false,
      dropType: '',
      dropIndex: -1,
      dropPosition: '' // 'before' 或 'after'
    });

    // 颜色组管理相关状态
    const saveColorGroupDialogVisible = ref(false);
    const selectColorGroupDialogVisible = ref(false);
    const renameDialogVisible = ref(false);
    const saveColorGroupLoading = ref(false);
    const renameLoading = ref(false);
    const activeColorGroupTab = ref('public');
    
    const saveColorGroupForm = reactive({
      name: '',
      type: 'private'
    });
    
    const renameForm = reactive({
      id: '',
      name: ''
    });
    
    const colorGroups = reactive({
      public: [],
      private: []
    });
    
    // 用户权限状态
    const isAdmin = ref(localStorage.getItem('userRole') === 'admin');

    // 矩阵数据
    const matrixData = reactive({
      products: [],
      goals: [],
      markets: [],
      visions: [],
      advantages: [],
      competitors: [],
      criteria: []
    });

    // 交汇关系
    const intersections = ref({});



    // 计算右侧列的总数
    const getTotalRightColumns = () => {
      return 1 + matrixData.markets.length + matrixData.competitors.length;
    };

    // 计算矩阵中心的colspan
    const getMatrixCenterColspan = () => {
      return 1;
    };

    // 计算竞争优势的colspan
    const getAdvantageColspan = () => {
      return 1 + matrixData.markets.length;
    };

    // 计算X矩阵的总列数
    const getXMatrixColumns = () => {
      return matrixData.goals.length + 1 + matrixData.markets.length;
    };

    // 计算K矩阵的总列数
    const getKMatrixColumns = () => {
      return 1 + matrixData.competitors.length;
    };

    // 获取交汇状态
    const getIntersectionState = (type, rowIndex, colIndex) => {
      const key = `${type}-${rowIndex}-${colIndex}`;
      return intersections.value[key] || false;
    };

    // 获取产品当前关联的市场索引（如果有的话）
    const getProductAssociatedMarket = (productIndex) => {
      for (let marketIndex = 0; marketIndex < matrixData.markets.length; marketIndex++) {
        const key = `product-market-${productIndex}-${marketIndex}`;
        if (intersections.value[key]) {
          return marketIndex;
        }
      }
      return null;
    };



    // 获取客户选购标准当前关联的市场索引（如果有的话）
    const getCriteriaAssociatedMarket = (criteriaIndex) => {
      for (let marketIndex = 0; marketIndex < matrixData.markets.length; marketIndex++) {
        const key = `criteria-market-${criteriaIndex}-${marketIndex}`;
        if (intersections.value[key]) {
          return marketIndex;
        }
      }
      return null;
    };

    // 检查客户选购标准是否已经关联了其他市场
    const isCriteriaAssociatedWithOtherMarket = (criteriaIndex, currentMarketIndex) => {
      const associatedMarket = getCriteriaAssociatedMarket(criteriaIndex);
      return associatedMarket !== null && associatedMarket !== currentMarketIndex;
    };

    // 获取客户选购标准条目的颜色（基于关联的市场）
    const getCriteriaColor = (criteriaIndex) => {
      const associatedMarketIndex = getCriteriaAssociatedMarket(criteriaIndex);
      if (associatedMarketIndex !== null) {
        // 如果客户选购标准关联了市场，使用市场的颜色
        return getMarketColor(associatedMarketIndex);
      }
      // 如果没有关联市场，使用默认的客户选购标准颜色
      return {
        primaryColor: '#80cbc4',
        secondaryColor: '#e0f2f1',
        bg: '#80cbc4',
        shadow: '#80cbc440'
      };
    };

    // 获取客户选购标准与市场交汇单元格的工具提示文本
    const getCriteriaMarketTooltip = (criteriaIndex, marketIndex) => {
      const criteriaName = matrixData.criteria[criteriaIndex]?.name || `选购标准${criteriaIndex + 1}`;
      const marketName = matrixData.markets[marketIndex]?.name || `市场${marketIndex + 1}`;

      if (getIntersectionState('criteria-market', criteriaIndex, marketIndex)) {
        return `${criteriaName} 已关联到 ${marketName}`;
      }

      const associatedMarketIndex = getCriteriaAssociatedMarket(criteriaIndex);
      if (associatedMarketIndex !== null && associatedMarketIndex !== marketIndex) {
        const associatedMarketName = matrixData.markets[associatedMarketIndex]?.name || `市场${associatedMarketIndex + 1}`;
        return `${criteriaName} 已关联到 ${associatedMarketName}，每个选购标准只能关联一个市场`;
      }

      return `点击关联 ${criteriaName} 到 ${marketName}`;
    };

    // 默认颜色配置
    const defaultColors = [
      { primaryColor: '#ffcc80', secondaryColor: '#fff3e0', name: '橙色' },
      { primaryColor: '#a5d6a7', secondaryColor: '#e8f5e8', name: '绿色' },
      { primaryColor: '#90caf9', secondaryColor: '#e3f2fd', name: '蓝色' },
      { primaryColor: '#f8bbd9', secondaryColor: '#fce4ec', name: '粉色' },
      { primaryColor: '#ce93d8', secondaryColor: '#f3e5f5', name: '紫色' },
      { primaryColor: '#ffcc02', secondaryColor: '#fff8e1', name: '黄色' },
      { primaryColor: '#80cbc4', secondaryColor: '#e0f2f1', name: '青色' },
      { primaryColor: '#ffcdd2', secondaryColor: '#ffebee', name: '红色' },
      { primaryColor: '#c5e1a5', secondaryColor: '#f1f8e9', name: '浅绿色' },
      { primaryColor: '#c5cae9', secondaryColor: '#e8eaf6', name: '靛蓝色' },
    ];

    // 根据索引生成递减深度的颜色
    const generateColorByDepth = (baseColor, index) => {
      // 将颜色转换为HSL格式以便调整亮度
      const hexToHsl = (hex) => {
        const r = parseInt(hex.slice(1, 3), 16) / 255;
        const g = parseInt(hex.slice(3, 5), 16) / 255;
        const b = parseInt(hex.slice(5, 7), 16) / 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
          h = s = 0;
        } else {
          const d = max - min;
          s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
          switch (max) {
            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
            case g: h = (b - r) / d + 2; break;
            case b: h = (r - g) / d + 4; break;
          }
          h /= 6;
        }

        return [h * 360, s * 100, l * 100];
      };

      const hslToHex = (h, s, l) => {
        h /= 360; s /= 100; l /= 100;
        const c = (1 - Math.abs(2 * l - 1)) * s;
        const x = c * (1 - Math.abs((h * 6) % 2 - 1));
        const m = l - c / 2;
        let r = 0, g = 0, b = 0;

        if (0 <= h && h < 1/6) { r = c; g = x; b = 0; }
        else if (1/6 <= h && h < 2/6) { r = x; g = c; b = 0; }
        else if (2/6 <= h && h < 3/6) { r = 0; g = c; b = x; }
        else if (3/6 <= h && h < 4/6) { r = 0; g = x; b = c; }
        else if (4/6 <= h && h < 5/6) { r = x; g = 0; b = c; }
        else if (5/6 <= h && h < 1) { r = c; g = 0; b = x; }

        r = Math.round((r + m) * 255);
        g = Math.round((g + m) * 255);
        b = Math.round((b + m) * 255);

        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
      };

      const [h, s, l] = hexToHsl(baseColor.primaryColor);

      // 第一个条目最深，后续条目逐渐变浅
      // 亮度递增：第0个条目保持原亮度，第1个条目+10%，第2个条目+20%，以此类推
      const lightnessIncrease = index * 8; // 每个条目亮度增加8%
      const newLightness = Math.min(l + lightnessIncrease, 85); // 最大亮度限制在85%

      return hslToHex(h, s, newLightness);
    };

    // 市场/客户条目的颜色数组（响应式）- 初始为空，在loadMatrix中初始化
    const marketColors = ref([]);

    // 获取市场条目的颜色
    const getMarketColor = (index) => {
      return marketColors.value[index % marketColors.value.length];
    };

    // 获取产品-市场交汇单元格的样式
    const getProductMarketCellStyle = (productIndex, marketIndex) => {
      console.log(`getProductMarketCellStyle called: Product ${productIndex} - Market ${marketIndex}`);

      if (productIndex === null || productIndex === undefined) {
        console.log('Product index is null/undefined');
        return {};
      }

      const isAssociated = getIntersectionState('product-market', productIndex, marketIndex);
      console.log(`Product ${productIndex} - Market ${marketIndex} associated:`, isAssociated);

      if (isAssociated) {
        const marketColor = getMarketColor(marketIndex);
        console.log(`Applying market color for market ${marketIndex}:`, marketColor);

        return {
          '--market-color': marketColor.bg,
          '--market-border-color': marketColor.primaryColor,
          backgroundColor: marketColor.bg
        };
      }

      return {};
    };

    // 获取产品条目的颜色（基于关联的市场）
    const getProductColor = (productIndex) => {
      const associatedMarketIndex = getProductAssociatedMarket(productIndex);
      if (associatedMarketIndex !== null) {
        // 如果产品关联了市场，使用市场的颜色
        return getMarketColor(associatedMarketIndex);
      }
      // 如果没有关联市场，使用默认的产品颜色
      return {
        primaryColor: '#90caf9',
        secondaryColor: '#e3f2fd',
        bg: '#90caf9',
        shadow: '#2196f340'
      };
    };

    // 检查某个单元格是否在产品-市场关联的路径上
    const isInProductMarketPath = (rowIndex, colType, colIndex) => {
      const productIndex = getProductIndex(rowIndex);

      // 只处理市场列的路径颜色
      if (colType === 'markets') {
        // 情况1：产品行的市场列路径 - 从产品条目到关联的市场交汇点（水平方向）
        if (productIndex !== null) {
          // 查找该产品关联的市场
          for (let marketIndex = 0; marketIndex < matrixData.markets.length; marketIndex++) {
            const isAssociated = getIntersectionState('product-market', productIndex, marketIndex);
            if (isAssociated) {
              // 从产品条目到交汇点的路径：产品行上从第一个市场列到关联的市场列
              if (colIndex <= marketIndex) {
                return marketIndex;
              }
            }
          }
        }

        // 情况2：市场列路径 - 从市场条目到关联的产品交汇点（垂直方向）
        // 查找与该市场关联的产品
        for (let pIndex = 0; pIndex < matrixData.products.length; pIndex++) {
          const isAssociated = getIntersectionState('product-market', pIndex, colIndex);
          if (isAssociated) {
            // 从市场条目到交汇点的路径：市场列上从关联的产品行到第一个产品行（方向相反）
            const currentProductIndex = getProductIndex(rowIndex);
            if (currentProductIndex !== null && currentProductIndex >= pIndex) {
              return colIndex;
            }
          }
        }
      }

      return null;
    };



    // 获取客户选购标准的实际索引（底部行）
    const getCriteriaIndex = (rowIndex) => {
      // 需要区分是否在客户选购标准行的上下文中
      // 通过检查调用栈来确定上下文
      const stack = new Error().stack;
      const isFromCriteriaRow = stack.includes('bottom-combined-row') || stack.includes('k-bottom-row');

      // 只有在客户选购标准行上下文中，且rowIndex在合理范围内时，才返回索引
      if (isFromCriteriaRow && rowIndex >= 1 && rowIndex <= matrixData.criteria.length) {
        return rowIndex - 1; // 转换为从0开始的索引
      }
      return null;
    };

    // 专门用于客户选购标准-市场关联的路径检查函数
    const isInCriteriaMarketPathOnly = (rowIndex, colType, colIndex) => {
      // 只处理市场列的路径颜色
      if (colType === 'markets') {
        // 直接使用rowIndex作为客户选购标准行的循环变量（1-3）
        if (rowIndex >= 1 && rowIndex <= matrixData.criteria.length) {
          const criteriaIndex = rowIndex - 1; // 转换为索引（0-2）

          // 情况1：水平路径 - 从关联的市场列到最后一个市场列
          for (let marketIndex = 0; marketIndex < matrixData.markets.length; marketIndex++) {
            const isAssociated = getIntersectionState('criteria-market', criteriaIndex, marketIndex);
            if (isAssociated) {
              // 从关联的市场列到最后一个市场列：条件 colIndex >= marketIndex
              if (colIndex >= marketIndex) {
                return marketIndex;
              }
            }
          }

          // 情况2：垂直路径 - 从第一个客户选购标准到关联的客户选购标准
          for (let cIndex = 0; cIndex < matrixData.criteria.length; cIndex++) {
            const isAssociated = getIntersectionState('criteria-market', cIndex, colIndex);
            if (isAssociated) {
              // 从第一个客户选购标准到关联的客户选购标准：条件 criteriaIndex <= cIndex
              if (criteriaIndex <= cIndex) {
                return colIndex;
              }
            }
          }
        }
      }

      return null;
    };

    // 原有的产品-市场路径检查函数（保持不变）
    const isInCriteriaMarketPath = (rowIndex, colType, colIndex) => {
      // 这个函数现在只用于非客户选购标准行的调用，直接返回null
      return null;
    };

    // 专门用于客户选购标准行的路径样式函数
    const getCriteriaPathCellStyle = (rowIndex, colType, colIndex) => {
      const criteriaMarketIndex = isInCriteriaMarketPathOnly(rowIndex, colType, colIndex);
      if (criteriaMarketIndex !== null) {
        const marketColor = getMarketColor(criteriaMarketIndex);
        console.log(`客户选购标准路径颜色: marketIndex=${criteriaMarketIndex}, marketColor.bg=${marketColor.bg}`);
        return `
          background: ${marketColor.bg} !important;
          background-color: ${marketColor.bg} !important;
          background-image: none !important;
          border-color: #ddd !important;
          opacity: 1 !important;
        `;
      }
      return '';
    };

    // 原有的路径样式函数（只用于产品行等其他行）
    const getPathCellStyle = (rowIndex, colType, colIndex) => {
      // 只检查产品-市场路径
      const productMarketIndex = isInProductMarketPath(rowIndex, colType, colIndex);
      if (productMarketIndex !== null) {
        const marketColor = getMarketColor(productMarketIndex);
        return `background-color: ${marketColor.bg} !important; border-color: #ddd !important;`;
      }

      return '';
    };



    // 更新颜色渐变
    const updateColorGradient = async (index) => {
      const color = marketColors.value[index];
      if (color.primaryColor && color.secondaryColor) {
        // 使用纯色背景而不是渐变，体现深浅层次
        color.bg = color.primaryColor;
        color.shadow = `${color.primaryColor}40`; // 添加透明度

        // 自动保存颜色配置
        try {
          await saveMatrix();
          console.log('颜色更新并保存成功');
        } catch (error) {
          console.error('保存颜色配置失败:', error);
        }
      }
    };

    // 初始化默认颜色（不保存）
    const initializeDefaultColors = () => {
      marketColors.value = defaultColors.map((color, index) => {
        const adjustedColor = generateColorByDepth(color, index);
        return {
          primaryColor: adjustedColor,
          secondaryColor: color.secondaryColor,
          name: color.name,
          bg: adjustedColor,
          shadow: `${adjustedColor}40`
        };
      });
    };

    // 重置为默认颜色（保存到数据库）
    const resetColors = async () => {
      initializeDefaultColors();

      // 自动保存颜色配置
      try {
        await saveMatrix();
        console.log('重置颜色并保存成功');
      } catch (error) {
        console.error('保存颜色配置失败:', error);
      }
    };

    // 随机生成颜色
    const randomizeColors = async () => {
      const generateRandomBaseColor = () => {
        const hue = Math.floor(Math.random() * 360);
        const saturation = 60 + Math.floor(Math.random() * 30); // 60-90%
        const lightness = 50 + Math.floor(Math.random() * 20); // 50-70% (基础深度)

        const hslToHex = (h, s, l) => {
          h /= 360; s /= 100; l /= 100;
          const c = (1 - Math.abs(2 * l - 1)) * s;
          const x = c * (1 - Math.abs((h * 6) % 2 - 1));
          const m = l - c / 2;
          let r = 0, g = 0, b = 0;

          if (0 <= h && h < 1/6) { r = c; g = x; b = 0; }
          else if (1/6 <= h && h < 2/6) { r = x; g = c; b = 0; }
          else if (2/6 <= h && h < 3/6) { r = 0; g = c; b = x; }
          else if (3/6 <= h && h < 4/6) { r = 0; g = x; b = c; }
          else if (4/6 <= h && h < 5/6) { r = x; g = 0; b = c; }
          else if (5/6 <= h && h < 1) { r = c; g = 0; b = x; }

          r = Math.round((r + m) * 255);
          g = Math.round((g + m) * 255);
          b = Math.round((b + m) * 255);

          return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
        };

        return {
          primaryColor: hslToHex(hue, saturation, lightness),
          secondaryColor: hslToHex(hue, saturation, lightness + 30) // 浅色版本
        };
      };

      // 生成一组基础颜色，然后根据索引调整深浅
      const baseColors = Array.from({ length: marketColors.value.length }, generateRandomBaseColor);

      marketColors.value.forEach((color, index) => {
        const baseColor = baseColors[index];
        const adjustedColor = generateColorByDepth(baseColor, index);
        color.primaryColor = adjustedColor;
        color.secondaryColor = baseColor.secondaryColor;
        color.bg = adjustedColor;
        color.shadow = `${adjustedColor}40`;
      });

      // 自动保存颜色配置
      try {
        await saveMatrix();
        console.log('随机颜色生成并保存成功');
      } catch (error) {
        console.error('保存颜色配置失败:', error);
      }
    };

    // 切换交汇关系
    const toggleIntersection = async (type, rowIndex, colIndex) => {
      try {
        // 验证索引有效性
        if (rowIndex === null || rowIndex === undefined || colIndex === null || colIndex === undefined) {
          console.error('无效的索引:', { type, rowIndex, colIndex });
          ElMessage.error('索引无效，无法设置关联关系');
          return;
        }

        const key = `${type}-${rowIndex}-${colIndex}`;
        console.log('切换交汇关系:', { type, rowIndex, colIndex, key });

        // 特殊处理：产品与市场的关联，每个产品只能关联一个市场
        if (type === 'product-market') {
          const currentValue = intersections.value[key] || false;

          if (!currentValue) {
            // 要设置为true时，先清除该产品与其他市场的关联
            Object.keys(intersections.value).forEach(existingKey => {
              if (existingKey.startsWith(`product-market-${rowIndex}-`) && existingKey !== key) {
                intersections.value[existingKey] = false;
              }
            });

            // 设置新的关联
            intersections.value[key] = true;
            console.log(`设置关联: ${key} = true`);
            console.log(`设置后检查: intersections.value['${key}'] =`, intersections.value[key]);
            ElMessage.success(`产品 "${matrixData.products[rowIndex]?.name}" 已关联到市场 "${matrixData.markets[colIndex]?.name}"`);
          } else {
            // 取消关联
            intersections.value[key] = false;
            ElMessage.info(`已取消产品 "${matrixData.products[rowIndex]?.name}" 与市场 "${matrixData.markets[colIndex]?.name}" 的关联`);
          }
        }
        // 特殊处理：客户选购标准与市场的关联，每个选购标准只能关联一个市场
        else if (type === 'criteria-market') {
          const currentValue = intersections.value[key] || false;

          if (!currentValue) {
            // 要设置为true时，先清除该选购标准与其他市场的关联
            Object.keys(intersections.value).forEach(existingKey => {
              if (existingKey.startsWith(`criteria-market-${rowIndex}-`) && existingKey !== key) {
                intersections.value[existingKey] = false;
              }
            });

            // 设置新的关联
            intersections.value[key] = true;
            ElMessage.success(`选购标准 "${matrixData.criteria[rowIndex]?.name}" 已关联到市场 "${matrixData.markets[colIndex]?.name}"`);
          } else {
            // 取消关联
            intersections.value[key] = false;
            ElMessage.info(`已取消选购标准 "${matrixData.criteria[rowIndex]?.name}" 与市场 "${matrixData.markets[colIndex]?.name}" 的关联`);
          }
        } else {
          // 其他类型的关联保持原有逻辑
          intersections.value[key] = !intersections.value[key];
        }

        // 自动保存
        await saveMatrix();
        // 交汇关系切换成功（通常不需要显示提示，因为用户可以直接看到变化）
      } catch (error) {
        console.error('切换交汇关系失败:', error);
        ElMessage.error('设置关联关系失败');
      }
    };

    // 加载矩阵数据
    const loadMatrix = async () => {
      try {
        loading.value = true;

        // 加载X矩阵数据
        const xMatrix = await projectApi.getXMatrix(props.projectId);

        // 加载K矩阵数据
        const kMatrix = await projectApi.getKMatrix(props.projectId);

        // 更新矩阵数据，并为没有order值的条目分配order值
        // 注意：保持现有的order值，只为没有order的条目分配新值
        // 对于已排序的数据，需要根据维度的排序规则来分配order值
        const ensureOrderValues = (items, type) => {
          return items.map((item, index) => {
            if (item.order !== undefined && item.order !== null) {
              return { ...item };
            }

            // 为没有order值的条目分配合适的order值
            // 产品与服务、竞争优势、目标使用降序排序（order值大的在前，显示在最外侧）
            // 其他维度使用升序排序（order值大的在后，显示在最外侧）
            let newOrder;
            if (type === 'products' || type === 'advantages' || type === 'goals') {
              // 降序排序：第一个条目应该有最大的order值
              newOrder = items.length - 1 - index;
            } else {
              // 升序排序：按索引分配order值
              newOrder = index;
            }

            return {
              ...item,
              order: newOrder
            };
          });
        };

        matrixData.products = ensureOrderValues(xMatrix.products || [], 'products');
        matrixData.goals = ensureOrderValues(xMatrix.goals || [], 'goals');
        matrixData.markets = ensureOrderValues(xMatrix.markets || [], 'markets');
        matrixData.visions = ensureOrderValues(xMatrix.visions || [], 'visions');
        matrixData.advantages = ensureOrderValues(kMatrix.advantages || [], 'advantages');
        matrixData.competitors = ensureOrderValues(kMatrix.competitors || [], 'competitors');
        matrixData.criteria = ensureOrderValues(kMatrix.criteria || [], 'criteria');

        // 转换关系数据为前端格式
        const allRelations = {};

        // 处理X矩阵关系
        if (xMatrix.relations) {
          xMatrix.relations.forEach(rel => {
            const key = `${rel.rowType.slice(0, -1)}-${rel.colType.slice(0, -1)}-${rel.rowIndex}-${rel.colIndex}`;
            console.log('加载X矩阵关系:', { original: rel, key });
            allRelations[key] = rel.value;
          });
        }

        // 处理K矩阵关系
        if (kMatrix.relations) {
          kMatrix.relations.forEach(rel => {
            // 使用类型映射转换category名称
            const typeMapping = {
              'advantages': 'advantage',
              'competitors': 'competitor',
              'criteria': 'criteria', // 修复：保持criteria不变，与保存时的键格式一致
              'markets': 'market',
              'products': 'product',
              'goals': 'goal',
              'visions': 'vision'
            };

            const getTypeKey = (categoryName) => typeMapping[categoryName] || categoryName.slice(0, -1);

            const key = `${getTypeKey(rel.category1)}-${getTypeKey(rel.category2)}-${rel.index1}-${rel.index2}`;
            console.log('加载K矩阵关系:', { original: rel, key });
            allRelations[key] = rel.value;
          });
        }

        intersections.value = allRelations;

        // 加载颜色配置
        if (xMatrix.colorSettings && xMatrix.colorSettings.marketColors) {
          console.log('加载保存的颜色配置:', xMatrix.colorSettings.marketColors);
          marketColors.value = xMatrix.colorSettings.marketColors;
        } else {
          console.log('未找到保存的颜色配置，使用默认颜色');
          // 如果没有保存的颜色配置，初始化为默认颜色（不保存）
          initializeDefaultColors();
        }

      } catch (err) {
        console.error('加载矩阵数据失败:', err);
        error.value = '加载矩阵数据失败';
      } finally {
        loading.value = false;
      }
    };

    // 保存矩阵
    const saveMatrix = async () => {
      try {
        console.log('开始保存矩阵...');
        console.log('当前矩阵数据:', matrixData);

        // 转换关系数据为后端格式
        const xMatrixRelations = [];
        const kMatrixRelations = [];

        console.log('当前所有交汇关系:', intersections.value);

        Object.keys(intersections.value).forEach(key => {
          if (intersections.value[key]) {
            const parts = key.split('-');
            console.log('处理关系键:', key, '分割结果:', parts);
            
            if (parts.length === 4) {
              const [type1, type2, index1, index2] = parts;

              // X矩阵关系 - 正确映射：产品是行，目标/市场是列；愿景只能与目标关联，不能与市场关联
              if ((type1 === 'product' && ['goal', 'market'].includes(type2)) ||
                  (type1 === 'vision' && type2 === 'goal')) {
                const relation = {
                  rowType: type1 + 's', // products 或 visions
                  rowIndex: parseInt(index1),
                  colType: type2 + 's', // goals 或 markets  
                  colIndex: parseInt(index2),
                  value: true
                };
                console.log('添加X矩阵关系:', relation);
                xMatrixRelations.push(relation);
              }
              // K矩阵关系 - 包括客户选购标准与市场的关联
              else if (['advantage', 'competitor', 'criteria'].includes(type1) &&
                       ['advantage', 'competitor', 'criteria', 'market'].includes(type2)) {
                // 正确映射category名称，避免criteria变成criterias
                const getCategoryName = (type) => {
                  switch(type) {
                    case 'advantage': return 'advantages';
                    case 'competitor': return 'competitors';
                    case 'criteria': return 'criteria'; // 保持单数形式
                    case 'market': return 'markets';
                    default: return type + 's';
                  }
                };
                
                const relation = {
                  category1: getCategoryName(type1),
                  index1: parseInt(index1),
                  category2: getCategoryName(type2),
                  index2: parseInt(index2),
                  value: true
                };
                console.log('添加K矩阵关系:', relation);
                kMatrixRelations.push(relation);
              } else {
                console.warn('未识别的关系类型:', { type1, type2, key });
              }
            } else {
              console.error('关系键格式错误:', key, parts);
            }
          }
        });

        console.log('准备保存的X矩阵关系:', xMatrixRelations);
        console.log('准备保存的K矩阵关系:', kMatrixRelations);
        console.log('准备保存的颜色配置:', marketColors.value);

        // 临时简化：不过滤空条目，直接保存所有条目
        const xMatrixData = {
          products: matrixData.products.map(item => ({ ...item, order: item.order !== undefined ? item.order : 0 })),
          goals: matrixData.goals.map(item => ({ ...item, order: item.order !== undefined ? item.order : 0 })),
          markets: matrixData.markets.map(item => ({ ...item, order: item.order !== undefined ? item.order : 0 })),
          visions: matrixData.visions.map(item => ({ ...item, order: item.order !== undefined ? item.order : 0 })),
          relations: xMatrixRelations,
          // 保存颜色配置
          colorSettings: {
            marketColors: marketColors.value
          }
        };

        const kMatrixData = {
          advantages: matrixData.advantages.map(item => ({ ...item, order: item.order !== undefined ? item.order : 0 })),
          competitors: matrixData.competitors.map(item => ({ ...item, order: item.order !== undefined ? item.order : 0 })),
          criteria: matrixData.criteria.map(item => ({ ...item, order: item.order !== undefined ? item.order : 0 })),
          relations: kMatrixRelations
        };

        console.log('准备保存X矩阵数据:', xMatrixData);
        console.log('准备保存K矩阵数据:', kMatrixData);

        // 保存X矩阵 - 保持原有的order值，包括负数，不重新分配
        await projectApi.updateXMatrix(props.projectId, xMatrixData);
        console.log('X矩阵保存成功');

        // 保存K矩阵 - 保持原有的order值，包括负数，不重新分配
        await projectApi.updateKMatrix(props.projectId, kMatrixData);
        console.log('K矩阵保存成功');

        console.log('矩阵保存完全成功');
        // 注意：不在这里显示成功消息，由调用方决定是否显示
      } catch (err) {
        console.error('保存失败 - 详细错误信息:', err);
        console.error('错误堆栈:', err.stack);
        console.error('错误响应:', err.response);
        ElMessage.error(`保存失败: ${err.message || '未知错误'}`);
      }
    };



    // 调整关联关系索引（当数组前面插入元素时）
    const adjustIntersectionsForInsert = (affectedType, insertIndex) => {
      const newIntersections = {};

      // 类型映射：复数形式到单数形式
      const typeMapping = {
        'products': 'product',
        'goals': 'goal',
        'markets': 'market',
        'visions': 'vision',
        'advantages': 'advantage',
        'competitors': 'competitor',
        'criteria': 'criteria' // 统一为复数
      };

      const singularType = typeMapping[affectedType] || affectedType.slice(0, -1);

      Object.keys(intersections.value).forEach(key => {
        const parts = key.split('-');
        if (parts.length === 4) {
          const [type1, type2, index1, index2] = parts;
          let newKey = key;

          // 检查是否需要调整索引
          if (type1 === singularType) {
            const oldIndex = parseInt(index1);
            if (oldIndex >= insertIndex) {
              newKey = `${type1}-${type2}-${oldIndex + 1}-${index2}`;
            }
          } else if (type2 === singularType) {
            const oldIndex = parseInt(index2);
            if (oldIndex >= insertIndex) {
              newKey = `${type1}-${type2}-${index1}-${oldIndex + 1}`;
            }
          }

          newIntersections[newKey] = intersections.value[key];
        } else {
          newIntersections[key] = intersections.value[key];
        }
      });

      intersections.value = newIntersections;
    };

    // 直接添加条目（不弹窗）
    const addItemDirect = async (type) => {
      console.log('点击了添加按钮，类型：', type); // 调试日志

      // 产品与服务的特殊约束：只有当目标(定量)和市场与客户都至少有一个条目时，才能添加产品与服务
      if (type === 'products') {
        if (matrixData.goals.length === 0 || matrixData.markets.length === 0) {
          let missingItems = [];
          if (matrixData.goals.length === 0) missingItems.push('目标(定量)');
          if (matrixData.markets.length === 0) missingItems.push('市场与客户');

          ElMessage.warning(`请先添加 ${missingItems.join(' 和 ')} 条目，然后再添加产品与服务`);
          return;
        }
      }

      try {
        const currentItems = matrixData[type];

        // 对于产品与服务、竞争优势、目标，新条目需要显示在最外侧（index=0）
        if (type === 'products' || type === 'advantages' || type === 'goals') {
          // 这些维度使用降序排序，order值大的在前面（最外侧）
          // 所以新条目需要有最大的order值
          let maxOrder = null;

          // 找到当前维度的最大order值
          currentItems.forEach(item => {
            if (item.order !== undefined) {
              if (maxOrder === null || item.order > maxOrder) {
                maxOrder = item.order;
              }
            }
          });

          // 新条目的order值为最大值+1，确保排在最前面（最外侧）
          const newOrder = maxOrder !== null ? maxOrder + 1 : 0;

          // 在插入新条目前，先调整关联关系索引
          adjustIntersectionsForInsert(type, 0);

          // 添加新条目到数组开头
          matrixData[type].unshift({
            name: '',
            order: newOrder
          });

          // 新条目在index 0位置
          const newItemIndex = 0;

          // 自动进入编辑状态
          await nextTick();
          await startEdit(type, newItemIndex);
        } else {
          // 其他维度（市场、愿景、竞争对手、客户选购标准）保持原有逻辑
          let maxOrder = -1;

          // 找到当前维度的最大order值
          currentItems.forEach(item => {
            if (item.order !== undefined && item.order > maxOrder) {
              maxOrder = item.order;
            }
          });

          // 新条目的order值为最大值+1
          const newOrder = maxOrder + 1;

          // 添加新条目到数组末尾
          const newItemIndex = matrixData[type].push({
            name: '',
            order: newOrder
          }) - 1;

          // 自动进入编辑状态
          await nextTick();
          await startEdit(type, newItemIndex);
        }

        ElMessage.success(`已添加新${getTypeName(type)}，请填写名称`);
      } catch (err) {
        console.error('添加失败:', err);
        ElMessage.error('添加失败');
      }
    };

    // 在指定位置后添加条目
    const addItemAtPosition = async (type, afterIndex) => {
      console.log('在位置后添加条目，类型：', type, '位置：', afterIndex);

      // 产品与服务的特殊约束：只有当目标(定量)和市场与客户都至少有一个条目时，才能添加产品与服务
      if (type === 'products') {
        if (matrixData.goals.length === 0 || matrixData.markets.length === 0) {
          let missingItems = [];
          if (matrixData.goals.length === 0) missingItems.push('目标(定量)');
          if (matrixData.markets.length === 0) missingItems.push('市场与客户');

          ElMessage.warning(`请先添加 ${missingItems.join(' 和 ')} 条目，然后再添加产品与服务`);
          return;
        }
      }

      try {
        const currentItems = matrixData[type];
        const insertIndex = afterIndex + 1; // 在指定位置后插入

        // 计算新条目的order值
        let newOrder;

        if (type === 'products' || type === 'advantages' || type === 'goals') {
          // 这些维度使用降序排序，需要在当前条目和下一个条目之间插入
          const currentOrder = currentItems[afterIndex]?.order || 0;
          const nextOrder = insertIndex < currentItems.length ? (currentItems[insertIndex]?.order || 0) : -1;

          // 如果当前order和下一个order之间有空隙，使用中间值
          if (currentOrder > nextOrder + 1) {
            newOrder = Math.floor((currentOrder + nextOrder) / 2);
          } else {
            // 否则需要调整后续所有条目的order值
            newOrder = currentOrder - 0.5;
          }
        } else {
          // 其他维度使用升序排序
          const currentOrder = currentItems[afterIndex]?.order || 0;
          const nextOrder = insertIndex < currentItems.length ? (currentItems[insertIndex]?.order || 0) : currentOrder + 2;

          // 如果当前order和下一个order之间有空隙，使用中间值
          if (nextOrder > currentOrder + 1) {
            newOrder = Math.floor((currentOrder + nextOrder) / 2);
          } else {
            // 否则需要调整后续所有条目的order值
            newOrder = currentOrder + 0.5;
          }
        }

        // 在插入新条目前，先调整关联关系索引
        adjustIntersectionsForInsert(type, insertIndex);

        // 在指定位置插入新条目
        matrixData[type].splice(insertIndex, 0, {
          name: '',
          order: newOrder
        });

        // 自动进入编辑状态
        await nextTick();
        await startEdit(type, insertIndex);

        ElMessage.success(`已添加新${getTypeName(type)}，请填写名称`);
      } catch (err) {
        console.error('添加失败:', err);
        ElMessage.error('添加失败');
      }
    };



    // 开始编辑条目
    const startEdit = async (type, index) => {
      editingItem.type = type;
      editingItem.index = index;
      editingItem.name = matrixData[type][index].name;
      editingItem.originalName = matrixData[type][index].name;
      
      // 等待DOM更新后聚焦输入框
      await nextTick();
      const inputElement = document.querySelector('.item-input');
      if (inputElement) {
        inputElement.focus();
        inputElement.select();
      }
    };

    // 保存编辑
    const saveEdit = async () => {
      console.log('saveEdit 被调用，当前编辑状态:', {
        type: editingItem.type,
        index: editingItem.index,
        name: editingItem.name,
        originalName: editingItem.originalName
      });

      // 如果没有正在编辑的条目，直接返回（防止重复调用）
      if (!editingItem.type || editingItem.index === -1) {
        console.log('没有正在编辑的条目，跳过保存');
        return;
      }

      const trimmedName = editingItem.name.trim();

      if (trimmedName) {
        // 有内容时保存
        matrixData[editingItem.type][editingItem.index].name = trimmedName;

        try {
          // 自动保存
          await saveMatrix();
          // 注意：不调用loadMatrix()，避免重新排序破坏当前的order值

          // 保存成功提示
          ElMessage.success('保存成功');
        } catch (error) {
          console.error('保存矩阵失败:', error);
          ElMessage.error('保存失败');
          return; // 保存失败时不清理编辑状态，让用户可以重试
        }

        // 清理编辑状态（不调用cancelEdit，避免删除条目）
        editingItem.type = '';
        editingItem.index = -1;
        editingItem.name = '';
        editingItem.originalName = '';
      } else {
        // 内容为空时删除条目
        try {
          // 保存必要信息，避免在清理状态后丢失
          const currentType = editingItem.type;
          const currentIndex = editingItem.index;

          // 先调整关联关系索引
          adjustIntersectionsForDelete(currentType, currentIndex);

          // 然后删除条目
          matrixData[currentType].splice(currentIndex, 1);
          ElMessage.info(`已删除空白${getTypeName(currentType)}`);

          // 清理编辑状态
          editingItem.type = '';
          editingItem.index = -1;
          editingItem.name = '';
          editingItem.originalName = '';
        } catch (error) {
          console.error('删除条目失败:', error);
          ElMessage.error('删除失败');
        }
      }
    };

    // 取消编辑
    const cancelEdit = () => {
      console.log('cancelEdit 被调用，当前编辑状态:', {
        type: editingItem.type,
        index: editingItem.index,
        name: editingItem.name,
        originalName: editingItem.originalName
      });

      // 如果原始名称为空且用户取消编辑，删除该条目
      if (editingItem.originalName === '') {
        // 保存必要信息，避免在清理状态后丢失
        const currentType = editingItem.type;
        const currentIndex = editingItem.index;

        // 先调整关联关系索引
        adjustIntersectionsForDelete(currentType, currentIndex);

        // 然后删除条目
        matrixData[currentType].splice(currentIndex, 1);
        ElMessage.info(`已取消添加${getTypeName(currentType)}`);
      }

      // 清理编辑状态
      editingItem.type = '';
      editingItem.index = -1;
      editingItem.name = '';
      editingItem.originalName = '';
    };

    // 调整关联关系索引（当删除元素时）
    const adjustIntersectionsForDelete = (affectedType, deleteIndex) => {
      console.log('adjustIntersectionsForDelete 被调用:', { affectedType, deleteIndex });
      const newIntersections = {};

      // 类型映射：复数形式到单数形式
      const typeMapping = {
        'products': 'product',
        'goals': 'goal',
        'markets': 'market',
        'visions': 'vision',
        'advantages': 'advantage',
        'competitors': 'competitor',
        'criteria': 'criteria' // 统一为复数
      };

      const singularType = typeMapping[affectedType] || affectedType.slice(0, -1);

      Object.keys(intersections.value).forEach(key => {
        const parts = key.split('-');
        if (parts.length === 4) {
          const [type1, type2, index1, index2] = parts;
          let shouldKeep = true;
          let newKey = key;

          // 检查是否需要删除或调整索引
          if (type1 === singularType) {
            const oldIndex = parseInt(index1);
            if (oldIndex === deleteIndex) {
              // 删除与被删除条目相关的所有关联
              shouldKeep = false;
            } else if (oldIndex > deleteIndex) {
              // 调整索引
              newKey = `${type1}-${type2}-${oldIndex - 1}-${index2}`;
            }
          } else if (type2 === singularType) {
            const oldIndex = parseInt(index2);
            if (oldIndex === deleteIndex) {
              // 删除与被删除条目相关的所有关联
              shouldKeep = false;
            } else if (oldIndex > deleteIndex) {
              // 调整索引
              newKey = `${type1}-${type2}-${index1}-${oldIndex - 1}`;
            }
          }
          
          if (shouldKeep) {
            newIntersections[newKey] = intersections.value[key];
          }
        } else {
          newIntersections[key] = intersections.value[key];
        }
      });
      
      intersections.value = newIntersections;
    };

    // 删除条目
    const deleteItem = async (type, index) => {
      // 先调整关联关系索引
      adjustIntersectionsForDelete(type, index);

      // 然后删除条目
      matrixData[type].splice(index, 1);

      // 自动保存
      await saveMatrix();
      ElMessage.success('删除成功');
      // 注意：不调用loadMatrix()，保持当前的排序
    };

    // 拖拽开始
    const startDrag = (event, type, index) => {
      // 阻止在编辑状态下拖拽
      if (editingItem.type === type && editingItem.index === index) {
        event.preventDefault();
        return;
      }

      dragState.dragging = true;
      dragState.dragType = type;
      dragState.dragIndex = index;

      // 设置拖拽数据
      event.dataTransfer.setData('text/plain', JSON.stringify({
        type,
        index
      }));

      // 设置拖拽效果
      event.dataTransfer.effectAllowed = 'move';
    };

    // 拖拽悬停
    const handleDragOver = (event, type, index) => {
      // 只允许在同一维度内拖拽
      if (dragState.dragType !== type) {
        return;
      }

      // 不能拖拽到自己身上
      if (dragState.dragIndex === index) {
        return;
      }

      event.preventDefault();
      event.dataTransfer.dropEffect = 'move';

      // 计算拖拽位置（前面还是后面）
      const rect = event.currentTarget.getBoundingClientRect();
      let dropPosition = 'before';

      // 根据维度类型判断拖拽方向
      if (type === 'goals' || type === 'markets' || type === 'competitors') {
        // 垂直方向的维度，根据鼠标X位置判断
        const midX = rect.left + rect.width / 2;
        dropPosition = event.clientX > midX ? 'after' : 'before';
      } else {
        // 水平方向的维度，根据鼠标Y位置判断
        const midY = rect.top + rect.height / 2;
        dropPosition = event.clientY > midY ? 'after' : 'before';
      }

      dragState.showDropIndicator = true;
      dragState.dropType = type;
      dragState.dropIndex = index;
      dragState.dropPosition = dropPosition;
    };

    // 拖拽放置
    const handleDrop = async (event, type, index) => {
      event.preventDefault();

      // 只允许在同一维度内拖拽
      if (dragState.dragType !== type) {
        return;
      }

      // 不能拖拽到自己身上
      if (dragState.dragIndex === index) {
        return;
      }

      const dragIndex = dragState.dragIndex;
      const dropIndex = index;
      const dropPosition = dragState.dropPosition;

      // 计算新的目标位置
      let newIndex = dropIndex;
      if (dropPosition === 'after') {
        newIndex = dropIndex + 1;
      }

      // 如果拖拽的条目在目标位置之前，需要调整索引
      if (dragIndex < newIndex) {
        newIndex--;
      }

      // 执行移动
      await moveItem(type, dragIndex, newIndex);
    };

    // 拖拽结束
    const endDrag = () => {
      dragState.dragging = false;
      dragState.dragType = '';
      dragState.dragIndex = -1;
      dragState.showDropIndicator = false;
      dragState.dropType = '';
      dragState.dropIndex = -1;
      dragState.dropPosition = '';
    };

    // 移动条目
    const moveItem = async (type, fromIndex, toIndex) => {
      if (fromIndex === toIndex) {
        return;
      }

      const items = matrixData[type];

      // 移动条目
      const [movedItem] = items.splice(fromIndex, 1);
      items.splice(toIndex, 0, movedItem);

      // 重新计算order值
      items.forEach((item, index) => {
        // 根据维度类型确定order值的计算方式
        if (type === 'products' || type === 'advantages' || type === 'goals') {
          // 这些维度使用降序排序（order值大的在前，显示在最外侧）
          item.order = items.length - 1 - index;
        } else {
          // 其他维度使用升序排序（order值大的在后，显示在最外侧）
          item.order = index;
        }
      });

      // 调整关联关系的索引
      adjustIntersectionsForMove(type, fromIndex, toIndex);

      // 自动保存
      await saveMatrix();
      ElMessage.success('移动成功');
    };

    // 调整移动后的关联关系索引
    const adjustIntersectionsForMove = (affectedType, fromIndex, toIndex) => {
      const newIntersections = {};

      // 类型映射：复数形式到单数形式
      const typeMapping = {
        'products': 'product',
        'goals': 'goal',
        'markets': 'market',
        'visions': 'vision',
        'advantages': 'advantage',
        'competitors': 'competitor',
        'criteria': 'criteria' // 统一为复数
      };

      const singularType = typeMapping[affectedType] || affectedType.slice(0, -1);

      Object.keys(intersections.value).forEach(key => {
        const parts = key.split('-');
        if (parts.length === 4) {
          const [type1, type2, index1, index2] = parts;
          let newKey = key;

          // 检查是否需要调整索引
          if (type1 === singularType) {
            const oldIndex = parseInt(index1);
            let newIndex = oldIndex;

            if (oldIndex === fromIndex) {
              // 被移动的条目
              newIndex = toIndex;
            } else if (fromIndex < toIndex) {
              // 向后移动：fromIndex到toIndex之间的条目索引减1
              if (oldIndex > fromIndex && oldIndex <= toIndex) {
                newIndex = oldIndex - 1;
              }
            } else {
              // 向前移动：toIndex到fromIndex之间的条目索引加1
              if (oldIndex >= toIndex && oldIndex < fromIndex) {
                newIndex = oldIndex + 1;
              }
            }

            newKey = `${type1}-${type2}-${newIndex}-${index2}`;
          } else if (type2 === singularType) {
            const oldIndex = parseInt(index2);
            let newIndex = oldIndex;

            if (oldIndex === fromIndex) {
              // 被移动的条目
              newIndex = toIndex;
            } else if (fromIndex < toIndex) {
              // 向后移动：fromIndex到toIndex之间的条目索引减1
              if (oldIndex > fromIndex && oldIndex <= toIndex) {
                newIndex = oldIndex - 1;
              }
            } else {
              // 向前移动：toIndex到fromIndex之间的条目索引加1
              if (oldIndex >= toIndex && oldIndex < fromIndex) {
                newIndex = oldIndex + 1;
              }
            }

            newKey = `${type1}-${type2}-${index1}-${newIndex}`;
          }

          newIntersections[newKey] = intersections.value[key];
        } else {
          newIntersections[key] = intersections.value[key];
        }
      });

      intersections.value = newIntersections;
    };

    // 获取产品的实际索引（从下方开始渲染）
    const getProductIndex = (rowIndex) => {
      const productCount = matrixData.products.length;
      const advantageCount = matrixData.advantages.length;
      
      // 如果产品数量少于竞争优势数量，从下方开始渲染
      if (productCount < advantageCount) {
        const offset = advantageCount - productCount;
        if (rowIndex > offset) {
          return rowIndex - offset - 1;
        }
        return null;
      } else {
        // 如果产品数量大于等于竞争优势数量，正常渲染
        if (rowIndex <= productCount) {
          return rowIndex - 1;
        }
        return null;
      }
    };

    // 获取竞争优势的实际索引（从下方开始渲染）
    const getAdvantageIndex = (rowIndex) => {
      const maxRows = Math.max(matrixData.products.length, matrixData.advantages.length);
      const advantageCount = matrixData.advantages.length;
      const productCount = matrixData.products.length;
      
      // 如果竞争优势数量少于产品数量，从下方开始渲染
      if (advantageCount < productCount) {
        const offset = productCount - advantageCount;
        if (rowIndex > offset) {
          return rowIndex - offset - 1;
        }
        return null;
      } else {
        // 如果竞争优势数量大于等于产品数量，正常渲染
        if (rowIndex <= advantageCount) {
          return rowIndex - 1;
        }
        return null;
      }
    };

    // 获取类别显示名称
    const getCategoryDisplayName = (type) => {
      const names = {
        products: '产品/服务',
        goals: '目标',
        markets: '市场/客户',
        visions: '愿景/目的',
        advantages: '竞争优势',
        competitors: '竞争对手',
        criteria: '选购标准'
      };
      return names[type] || type;
    };

    // 获取类型名称（用于临时名称生成）
    const getTypeName = (categoryName) => {
      const names = {
        products: '产品',
        goals: '目标',
        markets: '市场',
        visions: '愿景',
        advantages: '优势',
        competitors: '对手',
        criteria: '标准'
      };
      return names[categoryName] || categoryName;
    };

    // 同步到明道云
    const syncToMingdao = async () => {
      try {
        syncLoading.value = true;

        // 准备XK矩阵数据
        const xkMatrixData = {
          projectId: props.projectId,
          projectNumber: props.projectNumber || '', // 使用手动填写的项目编号
          projectName: props.projectName,
          // 生成项目XK矩阵直接访问链接
          projectUrl: `${window.location.origin}/projects/${props.projectId}`,
          // X矩阵数据
          xMatrix: {
            products: matrixData.products.map((item, index) => ({
              id: item.id || index,
              name: item.name,
              order: item.order || index,
              category: 'products'
            })),
            goals: matrixData.goals.map((item, index) => ({
              id: item.id || index,
              name: item.name,
              order: item.order || index,
              category: 'goals'
            })),
            markets: matrixData.markets.map((item, index) => ({
              id: item.id || index,
              name: item.name,
              order: item.order || index,
              category: 'markets'
            })),
            visions: matrixData.visions.map((item, index) => ({
              id: item.id || index,
              name: item.name,
              order: item.order || index,
              category: 'visions'
            }))
          },
          // K矩阵数据
          kMatrix: {
            advantages: matrixData.advantages.map((item, index) => ({
              id: item.id || index,
              name: item.name,
              order: item.order || index,
              category: 'advantages'
            })),
            competitors: matrixData.competitors.map((item, index) => ({
              id: item.id || index,
              name: item.name,
              order: item.order || index,
              category: 'competitors'
            })),
            criteria: matrixData.criteria.map((item, index) => ({
              id: item.id || index,
              name: item.name,
              order: item.order || index,
              category: 'criteria'
            }))
          },
          // 关联关系数据
          relationships: [],
          // 统计信息
          statistics: {
            totalItems: matrixData.products.length + matrixData.goals.length + 
                       matrixData.markets.length + matrixData.visions.length +
                       matrixData.advantages.length + matrixData.competitors.length + 
                       matrixData.criteria.length,
            xMatrixItems: matrixData.products.length + matrixData.goals.length + 
                         matrixData.markets.length + matrixData.visions.length,
            kMatrixItems: matrixData.advantages.length + matrixData.competitors.length + 
                         matrixData.criteria.length,
            totalRelationships: 0
          },
          // 同步时间
          syncTime: new Date().toISOString(),
          syncTimestamp: Date.now()
        };

        // 收集所有关联关系
        const relationships = [];
        let relationshipCount = 0;

        // 按类型分类收集关联关系
        const relationshipsByType = {
          xMatrix: [], // X矩阵内部关系
          kMatrix: [], // K矩阵内部关系
          crossMatrix: [] // 跨矩阵关系（如客户选购标准与市场）
        };

        // 遍历所有交汇状态，收集已勾选的关系
        Object.keys(intersections.value).forEach(key => {
          if (intersections.value[key]) {
            const parts = key.split('-');
            if (parts.length >= 4) {
              const [type1, type2, index1, index2] = parts;
              
              const relationship = {
                id: relationshipCount++,
                type1: type1,
                type2: type2,
                index1: parseInt(index1),
                index2: parseInt(index2),
                item1Name: getItemNameByTypeAndIndex(type1, parseInt(index1)),
                item2Name: getItemNameByTypeAndIndex(type2, parseInt(index2)),
                relationshipKey: key,
                relationshipDescription: `${getItemNameByTypeAndIndex(type1, parseInt(index1))} ↔ ${getItemNameByTypeAndIndex(type2, parseInt(index2))}`,
                active: true,
                createdAt: new Date().toISOString(),
                matrixType: getMatrixType(type1, type2)
              };

              // 根据关系类型分类
              if (isXMatrixRelation(type1, type2)) {
                relationshipsByType.xMatrix.push(relationship);
              } else if (isKMatrixRelation(type1, type2)) {
                relationshipsByType.kMatrix.push(relationship);
              } else if (isCrossMatrixRelation(type1, type2)) {
                relationshipsByType.crossMatrix.push(relationship);
              }

              relationships.push(relationship);
            }
          }
        });

        // 更新关联关系数据结构
        xkMatrixData.relationships = relationships;
        xkMatrixData.relationshipsByType = relationshipsByType;
        xkMatrixData.statistics.totalRelationships = relationships.length;
        xkMatrixData.statistics.xMatrixRelationships = relationshipsByType.xMatrix.length;
        xkMatrixData.statistics.kMatrixRelationships = relationshipsByType.kMatrix.length;
        xkMatrixData.statistics.crossMatrixRelationships = relationshipsByType.crossMatrix.length;

        // 生成关联关系摘要
        xkMatrixData.relationshipSummary = {
          total: relationships.length,
          byMatrix: {
            'X矩阵关系': relationshipsByType.xMatrix.length,
            'K矩阵关系': relationshipsByType.kMatrix.length,
            '跨矩阵关系': relationshipsByType.crossMatrix.length
          },
          detailedBreakdown: {
            'X矩阵': {
              '产品与服务 ↔ 目标': relationshipsByType.xMatrix.filter(r => 
                (r.type1 === 'product' && r.type2 === 'goal') || (r.type1 === 'goal' && r.type2 === 'product')).length,
              '产品与服务 ↔ 市场与客户': relationshipsByType.xMatrix.filter(r => 
                (r.type1 === 'product' && r.type2 === 'market') || (r.type1 === 'market' && r.type2 === 'product')).length,
              '愿景/目的 ↔ 目标': relationshipsByType.xMatrix.filter(r => 
                (r.type1 === 'vision' && r.type2 === 'goal') || (r.type1 === 'goal' && r.type2 === 'vision')).length
            },
            'K矩阵': {
              '竞争优势 ↔ 竞争对手': relationshipsByType.kMatrix.filter(r => 
                (r.type1 === 'advantage' && r.type2 === 'competitor') || (r.type1 === 'competitor' && r.type2 === 'advantage')).length,
              '竞争优势 ↔ 客户选购标准': relationshipsByType.kMatrix.filter(r => 
                (r.type1 === 'advantage' && r.type2 === 'criteria') || (r.type1 === 'criteria' && r.type2 === 'advantage')).length
            },
            '跨矩阵': {
              '客户选购标准 ↔ 市场与客户': relationshipsByType.crossMatrix.filter(r => 
                (r.type1 === 'criteria' && r.type2 === 'market') || (r.type1 === 'market' && r.type2 === 'criteria')).length
            }
          }
        };

        console.log('准备同步到明道云的数据:', xkMatrixData);

        // 调用明道云同步API接口
        const response = await projectApi.syncToMingdao(xkMatrixData);
        
        console.log('明道云同步响应:', response);
        ElMessage.success('XK矩阵数据已成功同步到明道云！');
        
      } catch (error) {
        console.error('同步到明道云失败:', error);
        ElMessage.error('同步到明道云失败: ' + (error.message || '未知错误'));
      } finally {
        syncLoading.value = false;
      }
    };

    // 根据类型和索引获取条目名称的辅助函数
    const getItemNameByTypeAndIndex = (type, index) => {
      const typeMap = {
        'product': 'products',
        'goal': 'goals', 
        'market': 'markets',
        'vision': 'visions',
        'advantage': 'advantages',
        'competitor': 'competitors',
        'criteria': 'criteria'
      };
      
      const actualType = typeMap[type] || type + 's';
      const items = matrixData[actualType];
      
      if (items && items[index]) {
        return items[index].name;
      }
      
      return `未知${type}`;
    };

    // 判断是否为X矩阵关系
    const isXMatrixRelation = (type1, type2) => {
      const xMatrixTypes = ['product', 'goal', 'market', 'vision'];
      return xMatrixTypes.includes(type1) && xMatrixTypes.includes(type2) && 
             !((type1 === 'vision' && type2 === 'market') || (type1 === 'market' && type2 === 'vision'));
    };

    // 判断是否为K矩阵关系
    const isKMatrixRelation = (type1, type2) => {
      const kMatrixTypes = ['advantage', 'competitor', 'criteria'];
      return kMatrixTypes.includes(type1) && kMatrixTypes.includes(type2);
    };

    // 判断是否为跨矩阵关系
    const isCrossMatrixRelation = (type1, type2) => {
      return (type1 === 'criteria' && type2 === 'market') || 
             (type1 === 'market' && type2 === 'criteria');
    };

    // 获取关系的矩阵类型
    const getMatrixType = (type1, type2) => {
      if (isXMatrixRelation(type1, type2)) {
        return 'X矩阵';
      } else if (isKMatrixRelation(type1, type2)) {
        return 'K矩阵';
      } else if (isCrossMatrixRelation(type1, type2)) {
        return '跨矩阵';
      }
      return '未知';
    };

    // 计算当前活跃的关联关系总数
    const getTotalActiveRelationships = () => {
      return Object.keys(intersections.value).filter(key => intersections.value[key]).length;
    };

    // 检查客户选购标准是否有任何关联关系
    const hasCriteriaAnyAssociation = (criteriaIndex) => {
      // 检查与市场的关联
      for (let marketIndex = 0; marketIndex < matrixData.markets.length; marketIndex++) {
        if (getIntersectionState('criteria-market', criteriaIndex, marketIndex)) {
          return true;
        }
      }

      // 检查与竞争对手的关联
      for (let competitorIndex = 0; competitorIndex < matrixData.competitors.length; competitorIndex++) {
        if (getIntersectionState('criteria-competitor', criteriaIndex, competitorIndex)) {
          return true;
        }
      }

      // 检查与竞争优势的关联
      for (let advantageIndex = 0; advantageIndex < matrixData.advantages.length; advantageIndex++) {
        if (getIntersectionState('advantage-criteria', advantageIndex, criteriaIndex)) {
          return true;
        }
      }

      return false;
    };

    // 获取未关联的客户选购标准列表
    const getUnassociatedCriteria = () => {
      return matrixData.criteria.filter((criteria, index) => !hasCriteriaAnyAssociation(index));
    };

    // 检查是否有未关联的客户选购标准
    const hasUnassociatedCriteria = () => {
      return getUnassociatedCriteria().length > 0;
    };

    // 获取未关联的客户选购标准数量
    const getUnassociatedCriteriaCount = () => {
      return getUnassociatedCriteria().length;
    };

    // 为未关联的客户选购标准添加竞争优势条目（新版：不自动建立任何关联，优先选择与竞争对手关联最少的标准，增加source字段）
    const addAdvantageFromUnassociatedCriteria = async () => {
      // 调试输出
      console.log('addAdvantageFromUnassociatedCriteria: criteria:', matrixData.criteria.map(c => c.id || c._id));
      console.log('addAdvantageFromUnassociatedCriteria: advantages.criteriaId:', matrixData.advantages.map(a => a.criteriaId));
      // 统计每个客户选购标准与竞争对手的关联数量
      const criteriaWithCompetitorCount = matrixData.criteria.map((criteria, index) => {
        let competitorCount = 0;
        for (let competitorIndex = 0; competitorIndex < matrixData.competitors.length; competitorIndex++) {
          if (getIntersectionState('criteria-competitor', index, competitorIndex)) {
            competitorCount++;
          }
        }
        return {
          criteria,
          index,
          competitorCount
        };
      });
      // 过滤未被添加为竞争优势的标准（按后端id判断）
      const existingCriteriaIds = matrixData.advantages.filter(a => a.source === 'criteria' && a.criteriaId).map(a => a.criteriaId);
      const totalCompetitors = matrixData.competitors.length;
      let unaddedCriteria = criteriaWithCompetitorCount.filter(item => {
        // 只用后端id判断
        return item.criteria.id && !existingCriteriaIds.includes(item.criteria.id) && item.competitorCount < totalCompetitors;
      });
      if (unaddedCriteria.length === 0) {
        ElMessage.warning('所有客户选购标准都已被添加为竞争优势');
        return;
      }
      // 按与竞争对手关联数量升序排序，优先添加关联最少的
      unaddedCriteria.sort((a, b) => a.competitorCount - b.competitorCount);
      // 直接全部添加
      try {
        let addedCount = 0;
        for (let i = 0; i < unaddedCriteria.length; i++) {
          const { criteria } = unaddedCriteria[i];
          let maxOrder = -1;
          matrixData.advantages.forEach(item => {
            if (item.order !== undefined && item.order > maxOrder) {
              maxOrder = item.order;
            }
          });
          const newOrder = maxOrder + 1;
          matrixData.advantages.push({
            name: criteria.name,
            order: newOrder,
            source: 'criteria',
            criteriaId: criteria.id // 只用后端id
          });
          addedCount++;
        }
        await saveMatrix();
        ElMessage.success(`成功抽取了 ${addedCount} 个竞争优势条目（无自动关联）`);
        await loadMatrix();
      } catch (error) {
        console.error('添加竞争优势失败:', error);
        ElMessage.error('添加竞争优势失败');
      }
    };

    // 获取可抽取的客户选购标准数量（用后端id判断）
    const getExtractableCriteriaCount = () => {
      // 调试输出
      console.log('=== getExtractableCriteriaCount 调试信息 ===');
      console.log('criteria 数据:', matrixData.criteria.map(c => ({ name: c.name, id: c.id, _id: c._id })));
      console.log('advantages 数据:', matrixData.advantages.map(a => ({ name: a.name, source: a.source, criteriaId: a.criteriaId })));
      console.log('competitors 数量:', matrixData.competitors.length);

      const existingCriteriaIds = matrixData.advantages.filter(a => a.source === 'criteria' && a.criteriaId).map(a => a.criteriaId);
      console.log('已存在的 criteriaIds:', existingCriteriaIds);

      const totalCompetitors = matrixData.competitors.length;
      const criteriaWithCompetitorCount = matrixData.criteria.map((criteria, index) => {
        let competitorCount = 0;
        for (let competitorIndex = 0; competitorIndex < matrixData.competitors.length; competitorIndex++) {
          if (getIntersectionState('criteria-competitor', index, competitorIndex)) {
            competitorCount++;
          }
        }
        return {
          criteria,
          index,
          competitorCount
        };
      });

      console.log('criteria 与竞争对手关联统计:', criteriaWithCompetitorCount.map(item => ({
        name: item.criteria.name,
        id: item.criteria.id,
        competitorCount: item.competitorCount,
        totalCompetitors: totalCompetitors
      })));

      let unaddedCriteria = criteriaWithCompetitorCount.filter(item => {
        const hasId = !!item.criteria.id;
        const notExisting = !existingCriteriaIds.includes(item.criteria.id);
        const notFullyAssociated = item.competitorCount < totalCompetitors;

        console.log(`标准 "${item.criteria.name}": hasId=${hasId}, notExisting=${notExisting}, notFullyAssociated=${notFullyAssociated} (${item.competitorCount}/${totalCompetitors})`);

        return hasId && notExisting && notFullyAssociated;
      });

      console.log('可抽取的 criteria:', unaddedCriteria.map(item => item.criteria.name));
      console.log('可抽取数量:', unaddedCriteria.length);
      console.log('=== 调试信息结束 ===');

      return unaddedCriteria.length;
    };

    // 颜色组管理相关方法
    const loadColorGroups = async () => {
      try {
        const response = await colorGroupApi.getColorGroups();
        if (response.success) {
          colorGroups.public = response.data.public || [];
          colorGroups.private = response.data.private || [];
        }
      } catch (error) {
        console.error('加载颜色组失败:', error);
        ElMessage.error('加载颜色组失败');
      }
    };

    const showSaveColorGroupDialog = () => {
      saveColorGroupForm.name = '';
      saveColorGroupForm.type = 'private';
      saveColorGroupDialogVisible.value = true;
    };

    const saveColorGroup = async () => {
      if (!saveColorGroupForm.name.trim()) {
        ElMessage.warning('请输入颜色组名称');
        return;
      }

      try {
        saveColorGroupLoading.value = true;
        const colors = marketColors.value.map(color => ({
          primaryColor: color.primaryColor,
          secondaryColor: color.secondaryColor,
          bg: color.bg
        }));

        console.log('准备保存颜色组:', {
          name: saveColorGroupForm.name.trim(),
          type: saveColorGroupForm.type,
          colors: colors
        });

        const response = await colorGroupApi.createColorGroup({
          name: saveColorGroupForm.name.trim(),
          type: saveColorGroupForm.type,
          colors
        });

        console.log('保存颜色组响应:', response);

        if (response.success) {
          ElMessage.success('颜色组保存成功');
          saveColorGroupDialogVisible.value = false;
          await loadColorGroups(); // 重新加载颜色组列表
        }
      } catch (error) {
        console.error('保存颜色组失败:', error);
        console.error('错误详情:', error.response);
        const errorMsg = error.response?.data?.message || error.message || '保存颜色组失败';
        ElMessage.error(errorMsg);
      } finally {
        saveColorGroupLoading.value = false;
      }
    };

    const showSelectColorGroupDialog = async () => {
      await loadColorGroups();
      selectColorGroupDialogVisible.value = true;
    };

    const selectColorGroup = async (group) => {
      // 应用颜色组到当前市场
      if (group.colors && group.colors.length > 0) {
        try {
          // 确保颜色数组长度足够
          const colorsToApply = [...group.colors];
          while (colorsToApply.length < marketColors.value.length) {
            colorsToApply.push(colorsToApply[colorsToApply.length - 1] || defaultColors[0]);
          }
          
          // 应用颜色
          marketColors.value.forEach((color, index) => {
            if (colorsToApply[index]) {
              color.primaryColor = colorsToApply[index].primaryColor;
              color.secondaryColor = colorsToApply[index].secondaryColor;
              color.bg = colorsToApply[index].bg;
            }
          });
          
          // 保存到数据库
          await saveMatrix();
          
          ElMessage.success(`已应用颜色组：${group.name}`);
          selectColorGroupDialogVisible.value = false;
        } catch (error) {
          console.error('应用颜色组失败:', error);
          ElMessage.error('应用颜色组失败，请重试');
        }
      }
    };

    const showRenameDialog = (group) => {
      renameForm.id = group._id;
      renameForm.name = group.name;
      renameDialogVisible.value = true;
    };

    const renameColorGroup = async () => {
      if (!renameForm.name.trim()) {
        ElMessage.warning('请输入新的颜色组名称');
        return;
      }

      try {
        renameLoading.value = true;
        const response = await colorGroupApi.updateColorGroup(renameForm.id, {
          name: renameForm.name.trim()
        });

        if (response.success) {
          ElMessage.success('颜色组重命名成功');
          renameDialogVisible.value = false;
          await loadColorGroups(); // 重新加载颜色组列表
        }
      } catch (error) {
        console.error('重命名颜色组失败:', error);
        const errorMsg = error.response?.data?.message || '重命名颜色组失败';
        ElMessage.error(errorMsg);
      } finally {
        renameLoading.value = false;
      }
    };

    const deleteColorGroup = async (groupId) => {
      try {
        await ElMessageBox.confirm('确定要删除这个颜色组吗？', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        const response = await colorGroupApi.deleteColorGroup(groupId);
        if (response.success) {
          ElMessage.success('颜色组删除成功');
          await loadColorGroups(); // 重新加载颜色组列表
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除颜色组失败:', error);
          const errorMsg = error.response?.data?.message || '删除颜色组失败';
          ElMessage.error(errorMsg);
        }
      }
    };

    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    // 工具函数：生成唯一ID
    const generateId = () => '_' + Math.random().toString(36).substr(2, 9);

    // 确保每条criteria都有唯一id
    const ensureCriteriaIds = () => {
      matrixData.criteria.forEach(criteria => {
        if (!criteria.id) {
          criteria.id = generateId();
        }
      });
    };

    onMounted(() => {
      loadMatrix();
      loadColorGroups(); // 加载颜色组
    });

    return {
      loading,
      error,
      matrixData,
      editingItem,
      dragState,
      syncLoading,
      getTotalRightColumns,
      getMatrixCenterColspan,
      getAdvantageColspan,
      getXMatrixColumns,
      getKMatrixColumns,
      getIntersectionState,
      getProductAssociatedMarket,
      getCriteriaAssociatedMarket,
      isCriteriaAssociatedWithOtherMarket,
      getCriteriaColor,
      getCriteriaMarketTooltip,
      toggleIntersection,
      getProductIndex,
      getAdvantageIndex,
      getCriteriaIndex,
      isInCriteriaMarketPath,
      isInCriteriaMarketPathOnly,
      getCriteriaPathCellStyle,
      marketColors,
      getMarketColor,
      getProductColor,
      getProductMarketCellStyle,
      isInProductMarketPath,
      getPathCellStyle,

      updateColorGradient,
      resetColors,
      randomizeColors,
      addItemDirect,
      addItemAtPosition,
      startEdit,
      saveEdit,
      cancelEdit,
      deleteItem,
      saveMatrix,
      syncToMingdao,
      getCategoryDisplayName,
      getTypeName,
      getTotalActiveRelationships,
      // 客户选购标准相关方法
      hasCriteriaAnyAssociation,
      getUnassociatedCriteria,
      hasUnassociatedCriteria,
      getUnassociatedCriteriaCount,
      addAdvantageFromUnassociatedCriteria,
      // 拖拽相关方法
      startDrag,
      handleDragOver,
      handleDrop,
      endDrag,
      moveItem,
      // 新方法
      getExtractableCriteriaCount,
      // 颜色组管理相关方法和状态
      saveColorGroupDialogVisible,
      selectColorGroupDialogVisible,
      renameDialogVisible,
      saveColorGroupLoading,
      renameLoading,
      activeColorGroupTab,
      saveColorGroupForm,
      renameForm,
      colorGroups,
      isAdmin,
      showSaveColorGroupDialog,
      saveColorGroup,
      showSelectColorGroupDialog,
      selectColorGroup,
      showRenameDialog,
      renameColorGroup,
      deleteColorGroup,
      formatDate
    };
  }
});
</script>

<style scoped>
.xk-matrix-container {
  padding: 0;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #f8fafc 100%);
  min-height: 100vh;
}

.matrix-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 20px 0 20px;
  padding: 24px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px 16px 0 0;
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.3),
    0 4px 10px rgba(0, 0, 0, 0.1);
}

.header-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.header-buttons .el-button {
  font-weight: 600;
  padding: 10px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.header-buttons .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.matrix-title {
  margin: 0;
  color: white;
  font-size: 28px;
  font-weight: 700;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.loading-container, .error-container {
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 20px;
}

/* 统一表格容器 */
.unified-table-container {
  padding: 60px 40px;
  background: white;
  margin: 0 20px;
  border-radius: 0 0 16px 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  min-height: 600px;
}

/* 颜色控制面板 */
.color-control-panel {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 颜色组管理相关样式 */
.form-tip {
  margin-top: 8px;
  color: #666;
}

.color-preview-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.color-preview-item {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.color-group-tabs {
  max-height: 400px;
  overflow-y: auto;
}

.color-group-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.color-group-item {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.color-group-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.color-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.group-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.group-actions {
  display: flex;
  gap: 8px;
}

.color-group-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
}

.group-info {
  display: flex;
  justify-content: space-between;
  color: #999;
  font-size: 12px;
}

.color-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.color-panel-header h3 {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.color-panel-actions {
  display: flex;
  gap: 8px;
}

.color-groups {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-start;
}

.color-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 2px solid transparent;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
  min-width: 100px;
}

.color-group.active {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.color-group:not(.active) {
  opacity: 0.6;
}

.color-preview {
  width: 60px;
  height: 40px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.color-index {
  color: white;
  font-weight: bold;
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.color-controls {
  display: flex;
  gap: 4px;
  align-items: center;
}

.color-label {
  font-size: 12px;
  text-align: center;
  max-width: 80px;
  word-break: break-word;
  line-height: 1.2;
}

.color-label .inactive-label {
  color: #999;
  font-style: italic;
}



.unified-matrix-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.unified-matrix-table {
  border-collapse: collapse;
  border-spacing: 0;
  width: auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.unified-matrix-table td {
  border: 1px solid #ddd;
  padding: 0;
  position: relative;
}

/* 阻止表格行的默认悬停效果 - 使用更强制性的方法 */
.unified-matrix-table tr {
  transition: none !important;
  background: transparent !important;
}

.unified-matrix-table tr:hover {
  background: transparent !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 禁用表格行的鼠标事件，只允许特定单元格响应 */
.unified-matrix-table tr {
  pointer-events: none;
}

/* 重新启用条目单元格容器和条目单元格的鼠标事件 */
.unified-matrix-table .item-cell-container {
  pointer-events: auto;
}

.unified-matrix-table .item-cell {
  pointer-events: auto;
}

/* 重新启用交汇单元格的鼠标事件 */
.unified-matrix-table .intersection-cell {
  pointer-events: auto;
}

/* 交汇单元格样式 */
.intersection-cell {
  border: 1px solid #ddd;
  width: 50px;
  height: 50px;
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
  transform: none !important;
  box-shadow: none !important;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}



/* 删除所有旧的关联样式规则，使用全新的高优先级规则 */

/* 最高优先级的关联状态样式 - 确保背景色填充整个表格单元格 */
.unified-matrix-table td.intersection-cell.associated,
.unified-matrix-table tbody tr td.intersection-cell.associated,
html body .unified-matrix-table td.intersection-cell.associated {
  background: var(--market-color) !important;
  background-color: var(--market-color) !important;
  background-image: none !important;
  border-color: #ddd !important;
}

/* 强制覆盖Element Plus的成功色 */
.intersection-cell.associated[data-v-bfdfd909] {
  background: var(--market-color) !important;
  background-color: var(--market-color) !important;
  color: white !important;
  border-color: #ddd !important;
}

/* 路径单元格样式 - 让边框颜色与背景色一致，形成视觉上连续的路径 */
.unified-matrix-table td[style*="background-color"] {
  box-sizing: border-box !important;
}

/* 交汇单元格关联状态的边框颜色保持可见 */
.unified-matrix-table td.intersection-cell.associated {
  border-color: #ddd !important;
}

/* 确保内联样式的背景色能够正确显示 */
.unified-matrix-table .intersection-cell[style*="background"] {
  background: var(--market-color, inherit) !important;
}



.check-mark {
  color: white;
  font-weight: bold;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}



.intersection-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.intersection-check {
  color: #4caf50;
  font-size: 16px;
  font-weight: bold;
}

/* 条目单元格容器样式 */
.item-cell-container {
  background: transparent;
  border: none;
  padding: 0;
  position: relative;
}

/* 条目单元格样式 */
.item-cell {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 0;
  cursor: pointer;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 46px;
  margin: 0;
}

/* 移除通用的悬停效果，改为针对每种类型单独设置 */

/* 单元格内容 */
.cell-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 8px 6px;
  min-height: 28px;
  width: 100%;
  height: 100%;
}

.cell-content.vertical {
  flex-direction: column;
  justify-content: center;
  text-align: center;
  writing-mode: vertical-rl;
  text-orientation: upright;
  height: 50px;
  width: 50px;
  padding: 8px 6px;
}

.cell-content.vertical .item-text {
  writing-mode: vertical-rl;
  text-orientation: upright;
  white-space: nowrap;
  margin-bottom: 10px;
  font-size: 13px;
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-text {
  font-weight: 600;
  font-size: 14px;
  color: #374151;
  line-height: 1.2;
  word-break: break-word;
  cursor: pointer;
}



/* 编辑输入框样式 */
.item-input {
  background: white;
  border: 2px solid #3182ce;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  width: 100%;
  min-width: 80px;
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.item-input.vertical {
  writing-mode: vertical-rl;
  text-orientation: upright;
  height: 120px;
  width: 40px;
  padding: 8px 4px;
  text-align: center;
  font-size: 13px;
  letter-spacing: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 2px solid #3182ce;
}

.item-input:focus {
  border-color: #2b6cb0;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

.item-actions {
  display: none;
  gap: 4px;
  margin-top: 8px;
}

/* 操作按钮始终隐藏，不显示悬停效果 */

/* 删除图标样式 */
.delete-icon {
  position: absolute;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: none;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.delete-icon:hover {
  background: #dc2626;
  transform: scale(1.1);
}

/* 行条目的删除图标定位 */
.product-cell .delete-icon,
.vision-cell .delete-icon,
.advantage-cell .delete-icon,
.criteria-cell .delete-icon {
  top: -8px;
  right: -8px;
}

/* 列条目的删除图标定位 */
.goal-cell .delete-icon.vertical,
.market-cell .delete-icon.vertical,
.competitor-cell .delete-icon.vertical {
  top: -8px;
  right: -8px;
}



/* 不同类型单元格的颜色 - 禁用悬停效果 */
.product-cell {
  /* 背景颜色通过内联样式动态设置 */
  min-height: 50px;
  height: 50px;
}





.goal-cell {
  background: linear-gradient(135deg, #e8f5e8, #a5d6a7);
  min-width: 50px;
  max-width: 50px;
  width: 50px;
  position: relative;
}



.market-cell {
  /* 背景颜色通过内联样式动态设置 */
  min-width: 50px;
  max-width: 50px;
  width: 50px;
  position: relative;
}



.vision-cell {
  background: linear-gradient(135deg, #fce4ec, #f8bbd9);
  min-height: 50px;
  height: 50px;
}



.advantage-cell {
  background: linear-gradient(135deg, #f3e5f5, #ce93d8);
  min-height: 50px;
  height: 50px;
}



.competitor-cell {
  background: linear-gradient(135deg, #fff8e1, #ffcc02);
  min-width: 50px;
  max-width: 50px;
  width: 50px;
  position: relative;
}



.criteria-cell {
  background: linear-gradient(135deg, #e0f2f1, #80cbc4);
  min-height: 50px;
  height: 50px;
}



/* 矩阵中心样式 */
.matrix-center-cell {
  background: transparent !important;
  border: none;
  padding: 0;
  min-width: 320px;
  min-height: 240px;
  transform: none !important;
  box-shadow: none !important;
}

.matrix-center-cell:hover {
  background: transparent !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 矩阵宽度控制 */
.x-matrix-width {
  width: 320px;
  min-width: 320px;
  max-width: 320px;
}

.k-matrix-width {
  width: 320px;
  min-width: 320px;
  max-width: 320px;
}

/* 矩阵分隔符 */
.matrix-separator {
  background: transparent !important;
  border: none;
  width: 320px;
  min-width: 320px;
  max-width: 320px;
  transform: none !important;
  box-shadow: none !important;
}

.matrix-separator:hover {
  background: transparent !important;
  transform: none !important;
  box-shadow: none !important;
}

/* X矩阵和K矩阵之间的分隔列 */
.matrix-separator-column {
  background: transparent !important;
  border: none;
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  height: 100%;
  transform: none !important;
  box-shadow: none !important;
}

.matrix-separator-column:hover {
  background: transparent !important;
  transform: none !important;
  box-shadow: none !important;
}

.unified-matrix-center {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

.x-matrix-section,
.k-matrix-section {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.matrix-svg {
  width: 320px;
  height: 240px;
  background: transparent;
  border: none;
}

.area-label {
  font-size: 14px;
  font-weight: 600;
  fill: #333;
}

.area-label-chinese {
  font-size: 13px;
  font-weight: 500;
  fill: #666;
}

.matrix-label {
  margin-top: 10px;
  font-weight: 600;
  font-size: 16px;
  color: #333;
  text-align: center;
}

/* 角落添加按钮样式 */
.corner-add-btn {
  position: absolute;
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  transform: scale(0.8);
}

.corner-add-btn:hover {
  transform: scale(1);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  background: linear-gradient(135deg, #45a049, #4CAF50);
}

.corner-add-btn i {
  color: white;
  font-size: 14px;
  font-weight: bold;
}

/* 不同位置的角落按钮 */
.corner-add-btn.top-right {
  top: -12px;
  right: -12px;
}

.corner-add-btn.bottom-left {
  bottom: -12px;
  left: -12px;
}

.corner-add-btn.bottom-right {
  bottom: -12px;
  right: -12px;
}

.corner-add-btn.top-left {
  top: -12px;
  left: -12px;
}

/* 位置添加按钮样式 */
.position-add-btn {
  position: absolute;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #409EFF, #66B1FF);
  border: 1px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 8;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: scale(0.8);
}

.position-add-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, #66B1FF, #409EFF);
  opacity: 1;
}

/* 当鼠标悬停在条目上时显示位置添加按钮和删除按钮 */
.matrix-item:hover .position-add-btn {
  opacity: 0.8;
}

/* 为所有类型的单元格添加悬停显示删除按钮的规则 */
.product-cell:hover .delete-icon,
.vision-cell:hover .delete-icon,
.advantage-cell:hover .delete-icon,
.criteria-cell:hover .delete-icon,
.goal-cell:hover .delete-icon,
.market-cell:hover .delete-icon,
.competitor-cell:hover .delete-icon {
  display: flex;
}

.position-add-btn i {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 不同方向的位置添加按钮 */
.position-add-btn.right {
  top: 50%;
  right: -10px;
  transform: translateY(-50%) scale(0.8);
}

.position-add-btn.right:hover {
  transform: translateY(-50%) scale(1.1);
}

.position-add-btn.bottom {
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%) scale(0.8);
}

.position-add-btn.bottom:hover {
  transform: translateX(-50%) scale(1.1);
}



@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.5);
  }
  50% {
    box-shadow: 0 8px 20px rgba(255, 107, 107, 0.8);
  }
  100% {
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.5);
  }
}

/* 矩阵顶点添加按钮位置 - 空矩阵时显示在各个顶点 */
.matrix-corner-btn {
  background: linear-gradient(135deg, #FF6B6B, #FF5252) !important;
  animation: pulse 2s infinite;
  width: 32px !important;
  height: 32px !important;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.5) !important;
  z-index: 20 !important;
  pointer-events: auto !important;
}

.matrix-corner-btn:hover {
  box-shadow: 0 6px 18px rgba(255, 107, 107, 0.7) !important;
  filter: brightness(1.1);
}

.matrix-corner-btn .add-icon {
  color: white;
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
}

/* X矩阵顶点按钮位置 */
.matrix-corner-btn.products-corner {
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  transform-origin: center;
}

.matrix-corner-btn.goals-corner {
  top: 50%;
  left: -16px;
  transform: translateY(-50%);
  transform-origin: center;
}

.matrix-corner-btn.markets-corner {
  top: 50%;
  right: -16px;
  transform: translateY(-50%);
  transform-origin: center;
}

.matrix-corner-btn.visions-corner {
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  transform-origin: center;
}

/* K矩阵顶点按钮位置 */
.matrix-corner-btn.advantages-corner {
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  transform-origin: center;
}

.matrix-corner-btn.competitors-corner {
  top: 50%;
  right: -16px;
  transform: translateY(-50%);
  transform-origin: center;
}

.matrix-corner-btn.criteria-corner {
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  transform-origin: center;
}

/* 关联关系计数样式 */
.relationship-count {
  font-size: 12px;
  opacity: 0.9;
  margin-left: 4px;
}

/* 客户选购标准计数样式 */
.criteria-count {
  font-size: 12px;
  opacity: 0.9;
  margin-left: 4px;
}

/* 禁用状态的单元格样式 */
.disabled-cell {
  background: #f5f5f5 !important;
  color: #ccc !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.disabled-cell:hover {
  background: #f5f5f5 !important;
  transform: none !important;
  box-shadow: none !important;
}

.disabled-mark {
  color: #ccc;
  font-size: 18px;
  font-weight: bold;
}

/* 拖拽相关样式已移除 */

.item-cell.dragging {
  opacity: 0.5;
  transform: scale(0.95);
  z-index: 1000;
}

/* 拖拽手柄样式 */
.drag-handle {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  cursor: grab;
  opacity: 0;
  transition: opacity 0.2s ease;
  font-size: 10px;
  color: #666;
}

.drag-handle:hover {
  background: rgba(0, 0, 0, 0.2);
  opacity: 1;
}

.item-cell:hover .drag-handle {
  opacity: 0.7;
}

.drag-handle:active {
  cursor: grabbing;
}

/* 垂直方向的拖拽手柄 */
.drag-handle.vertical {
  top: 2px;
  left: 2px;
  right: auto;
}

/* 内联拖拽手柄样式 */
.drag-handle.inline {
  position: relative;
  display: inline-flex;
  top: auto;
  right: auto;
  left: auto;
  width: 14px;
  height: 14px;
  margin-left: 4px;
  vertical-align: middle;
  opacity: 0.4;
  font-size: 9px;
  background: rgba(0, 0, 0, 0.05);
}

.drag-handle.inline:hover {
  opacity: 0.8;
  background: rgba(0, 0, 0, 0.1);
}





.intersection-cell.disabled {
  background: #f5f5f5 !important;
  color: #ccc !important;
  cursor: not-allowed !important;
  opacity: 0.6;
  border: 1px solid #e4e7ed !important;
}

.intersection-cell.disabled:hover {
  background: #f5f5f5 !important;
  transform: none !important;
  box-shadow: none !important;
}

.intersection-cell.disabled .disabled-mark {
  color: #ccc;
  font-size: 16px;
  font-weight: bold;
}

/* 拖拽指示器样式 */
.drop-indicator {
  position: absolute;
  background: #409eff;
  z-index: 1001;
  pointer-events: none;
}

/* 水平方向的拖拽指示器（产品、竞争优势、愿景、客户选购标准） */
.drop-indicator.before {
  top: -2px;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 2px;
}

.drop-indicator.after {
  bottom: -2px;
  left: 0;
  right: 0;
  height: 4px;
  border-radius: 2px;
}

/* 垂直方向的拖拽指示器（目标、市场、竞争对手） */
.goal-cell .drop-indicator.before,
.market-cell .drop-indicator.before,
.competitor-cell .drop-indicator.before {
  top: 0;
  bottom: 0;
  left: -2px;
  width: 4px;
  height: auto;
  border-radius: 2px;
}

.goal-cell .drop-indicator.after,
.market-cell .drop-indicator.after,
.competitor-cell .drop-indicator.after {
  top: 0;
  bottom: 0;
  right: -2px;
  left: auto;
  width: 4px;
  height: auto;
  border-radius: 2px;
}

/* 拖拽时禁用文本选择 */
.item-cell[draggable="true"] {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}


</style>

