"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const db_1 = __importDefault(require("./config/db"));
const config_1 = __importDefault(require("./config/config"));
// 导入路由
const auth_1 = __importDefault(require("./routes/auth"));
const projects_1 = __importDefault(require("./routes/projects"));
const users_1 = __importDefault(require("./routes/users"));
const colorGroups_1 = __importDefault(require("./routes/colorGroups"));
// 连接数据库
(0, db_1.default)();
// 初始化Express应用
const app = (0, express_1.default)();
// 中间件
app.use((0, cors_1.default)({
    origin: true, // 允许所有域名（测试环境）
    credentials: true
}));
app.use(express_1.default.json());
// 请求日志中间件（仅开发环境）
if (config_1.default.nodeEnv === 'development') {
    app.use((req, res, next) => {
        console.log(`${req.method} ${req.path}`);
        next();
    });
}
// 路由
app.use('/api/auth', auth_1.default);
app.use('/api/projects', projects_1.default);
app.use('/api/users', users_1.default);
app.use('/api/color-groups', colorGroups_1.default);
// 错误处理中间件
app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({ message: '服务器内部错误', error: err.message });
});
// 基础路由
app.get('/', (req, res) => {
    res.send('XK矩阵API服务正在运行');
});
// 启动服务器
const PORT = config_1.default.port;
app.listen(PORT, () => {
    console.log(`服务器运行在端口 ${PORT}`);
});
exports.default = app;
