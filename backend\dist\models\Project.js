"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importStar(require("mongoose"));
// 分析点Schema
const AnalysisPointSchema = new mongoose_1.Schema({
    name: { type: String, required: true },
    description: { type: String },
    order: { type: Number, required: true, default: 0 },
    source: { type: String }, // 来源标识，如 'criteria' 表示从客户选购标准抽取
    criteriaId: { type: mongoose_1.Schema.Types.ObjectId }, // 关联的客户选购标准ID（单个）
    criteriaIds: [{ type: mongoose_1.Schema.Types.ObjectId }] // 关联的客户选购标准ID数组（用于合并后的竞争优势）
});
// X矩阵关系Schema
const MatrixRelationSchema = new mongoose_1.Schema({
    rowType: { type: String, required: true, enum: ['products', 'visions'] },
    rowIndex: { type: Number, required: true },
    colType: { type: String, required: true, enum: ['goals', 'markets'] },
    colIndex: { type: Number, required: true },
    value: { type: Boolean, required: true, default: false }
});
// K矩阵关系Schema
const KMatrixRelationSchema = new mongoose_1.Schema({
    category1: { type: String, required: true, enum: ['advantages', 'competitors', 'criteria'] },
    index1: { type: Number, required: true },
    category2: { type: String, required: true, enum: ['advantages', 'competitors', 'criteria', 'markets'] },
    index2: { type: Number, required: true },
    value: { type: Boolean, required: true, default: false }
});
// X矩阵Schema
const XMatrixSchema = new mongoose_1.Schema({
    products: [AnalysisPointSchema],
    markets: [AnalysisPointSchema],
    goals: [AnalysisPointSchema],
    visions: [AnalysisPointSchema],
    relations: [MatrixRelationSchema],
    colorSettings: { type: mongoose_1.Schema.Types.Mixed, default: null }
});
// K矩阵Schema
const KMatrixSchema = new mongoose_1.Schema({
    advantages: [AnalysisPointSchema],
    competitors: [AnalysisPointSchema],
    criteria: [AnalysisPointSchema],
    relations: [KMatrixRelationSchema]
});
// 项目Schema
const ProjectSchema = new mongoose_1.Schema({
    name: { type: String, required: true },
    projectNumber: { type: String, unique: true, sparse: true }, // 项目编号，唯一但可选
    description: { type: String },
    owner: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User', required: true },
    xMatrix: {
        type: XMatrixSchema,
        default: {
            products: [],
            markets: [],
            goals: [],
            visions: [],
            relations: [],
            colorSettings: null
        }
    },
    kMatrix: {
        type: KMatrixSchema,
        default: {
            advantages: [],
            competitors: [],
            criteria: [],
            relations: []
        }
    }
}, { timestamps: true });
// 创建并导出项目模型
exports.default = mongoose_1.default.model('Project', ProjectSchema);
