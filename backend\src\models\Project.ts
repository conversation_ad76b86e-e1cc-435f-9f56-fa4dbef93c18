import mongoose, { Document, Schema } from 'mongoose';

// 分析点接口
interface IAnalysisPoint {
  _id?: string;
  name: string;
  description?: string;
  order: number; // 添加显示顺序
}

// 矩阵关系接口 - 使用坐标系统
interface IMatrixRelation {
  rowType: string;  // 行类型：'products' | 'visions'
  rowIndex: number; // 行索引
  colType: string;  // 列类型：'goals' | 'markets'
  colIndex: number; // 列索引
  value: boolean;   // 关系值
}

// X矩阵接口
interface IXMatrix {
  products: IAnalysisPoint[];     // 产品与服务（顶部列）
  markets: IAnalysisPoint[];      // 市场与客户（右侧列）
  goals: IAnalysisPoint[];        // 目标（左侧行）
  visions: IAnalysisPoint[];      // 愿景/目的（底部行）
  relations: IMatrixRelation[];   // 矩阵关系
  colorSettings?: any;            // 颜色配置
}

// K矩阵关系接口
interface IKMatrixRelation {
  category1: string; // 第一个分析点类别
  index1: number;    // 第一个分析点索引
  category2: string; // 第二个分析点类别
  index2: number;    // 第二个分析点索引
  value: boolean;    // 关系值
}

// K矩阵接口
interface IKMatrix {
  advantages: IAnalysisPoint[];    // 竞争优势
  competitors: IAnalysisPoint[];   // 竞争对手
  criteria: IAnalysisPoint[];      // 客户选购标准
  relations: IKMatrixRelation[];   // 关联关系
}

// 项目接口
export interface IProject extends Document {
  name: string;
  projectNumber?: string; // 项目编号
  description?: string;
  owner: mongoose.Types.ObjectId;
  xMatrix: IXMatrix;
  kMatrix: IKMatrix;
  createdAt: Date;
  updatedAt: Date;
}

// 分析点Schema
const AnalysisPointSchema = new Schema({
  name: { type: String, required: true },
  description: { type: String },
  order: { type: Number, required: true, default: 0 },
  source: { type: String }, // 来源标识，如 'criteria' 表示从客户选购标准抽取
  criteriaId: { type: Schema.Types.ObjectId } // 关联的客户选购标准ID
});

// X矩阵关系Schema
const MatrixRelationSchema = new Schema({
  rowType: { type: String, required: true, enum: ['products', 'visions'] },
  rowIndex: { type: Number, required: true },
  colType: { type: String, required: true, enum: ['goals', 'markets'] },
  colIndex: { type: Number, required: true },
  value: { type: Boolean, required: true, default: false }
});

// K矩阵关系Schema
const KMatrixRelationSchema = new Schema({
  category1: { type: String, required: true, enum: ['advantages', 'competitors', 'criteria'] },
  index1: { type: Number, required: true },
  category2: { type: String, required: true, enum: ['advantages', 'competitors', 'criteria', 'markets'] },
  index2: { type: Number, required: true },
  value: { type: Boolean, required: true, default: false }
});

// X矩阵Schema
const XMatrixSchema = new Schema({
  products: [AnalysisPointSchema],
  markets: [AnalysisPointSchema],
  goals: [AnalysisPointSchema],
  visions: [AnalysisPointSchema],
  relations: [MatrixRelationSchema],
  colorSettings: { type: Schema.Types.Mixed, default: null }
});

// K矩阵Schema
const KMatrixSchema = new Schema({
  advantages: [AnalysisPointSchema],
  competitors: [AnalysisPointSchema],
  criteria: [AnalysisPointSchema],
  relations: [KMatrixRelationSchema]
});

// 项目Schema
const ProjectSchema: Schema = new Schema(
  {
    name: { type: String, required: true },
    projectNumber: { type: String, unique: true, sparse: true }, // 项目编号，唯一但可选
    description: { type: String },
    owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    xMatrix: {
      type: XMatrixSchema,
      default: {
        products: [],
        markets: [],
        goals: [],
        visions: [],
        relations: [],
        colorSettings: null
      }
    },
    kMatrix: {
      type: KMatrixSchema,
      default: {
        advantages: [],
        competitors: [],
        criteria: [],
        relations: []
      }
    }
  },
  { timestamps: true }
);

// 创建并导出项目模型
export default mongoose.model<IProject>('Project', ProjectSchema); 