"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const colorGroupController_1 = require("../controllers/colorGroupController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有颜色组路由都需要认证
router.use(auth_1.auth);
// 获取颜色组列表
router.get('/', colorGroupController_1.getColorGroups);
// 创建颜色组
router.post('/', colorGroupController_1.createColorGroup);
// 更新颜色组
router.put('/:id', colorGroupController_1.updateColorGroup);
// 删除颜色组
router.delete('/:id', colorGroupController_1.deleteColorGroup);
exports.default = router;
